{"remainingRequest": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue", "mtime": 1754099032596}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754057742170}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754057742816}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754057742170}, {"path": "D:\\LanguageProjects\\u3w-ai\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754057742426}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBtYXJrZWQgfSBmcm9tICJtYXJrZWQiOw0KaW1wb3J0IHsNCiAgbWVzc2FnZSwNCiAgc2F2ZVVzZXJDaGF0RGF0YSwNCiAgZ2V0Q2hhdEhpc3RvcnksDQogIHB1c2hBdXRvT2ZmaWNlLA0KICBnZXRNZWRpYUNhbGxXb3JkLA0KfSBmcm9tICJAL2FwaS93ZWNoYXQvYWlnYyI7DQppbXBvcnQgeyB2NCBhcyB1dWlkdjQgfSBmcm9tICJ1dWlkIjsNCmltcG9ydCB3ZWJzb2NrZXRDbGllbnQgZnJvbSAiQC91dGlscy93ZWJzb2NrZXQiOw0KaW1wb3J0IHN0b3JlIGZyb20gIkAvc3RvcmUiOw0KaW1wb3J0IFR1cm5kb3duU2VydmljZSBmcm9tICJ0dXJuZG93biI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkFJTWFuYWdlbWVudFBsYXRmb3JtIiwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgdXNlcklkOiBzdG9yZS5zdGF0ZS51c2VyLmlkLA0KICAgICAgY29ycElkOiBzdG9yZS5zdGF0ZS51c2VyLmNvcnBfaWQsDQogICAgICBjaGF0SWQ6IHV1aWR2NCgpLA0KICAgICAgZXhwYW5kZWRIaXN0b3J5SXRlbXM6IHt9LA0KICAgICAgdXNlckluZm9SZXE6IHsNCiAgICAgICAgdXNlclByb21wdDogIiIsDQogICAgICAgIHVzZXJJZDogIiIsDQogICAgICAgIGNvcnBJZDogIiIsDQogICAgICAgIHRhc2tJZDogIiIsDQogICAgICAgIHJvbGVzOiAiIiwNCiAgICAgICAgdG9uZUNoYXRJZDogIiIsDQogICAgICAgIHliRHNDaGF0SWQ6ICIiLA0KICAgICAgICBkYkNoYXRJZDogIiIsDQogICAgICAgIHR5Q2hhdElkOiAiIiwNCiAgICAgICAgaXNOZXdDaGF0OiB0cnVlLA0KICAgICAgfSwNCiAgICAgIGpzb25ScGNSZXFlc3Q6IHsNCiAgICAgICAganNvbnJwYzogIjIuMCIsDQogICAgICAgIGlkOiB1dWlkdjQoKSwNCiAgICAgICAgbWV0aG9kOiAiIiwNCiAgICAgICAgcGFyYW1zOiB7fSwNCiAgICAgIH0sDQogICAgICBhaUxpc3Q6IFsNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICJEZWVwU2VlayIsDQogICAgICAgICAgYXZhdGFyOiByZXF1aXJlKCIuLi8uLi8uLi9hc3NldHMvbG9nby9EZWVwc2Vlay5wbmciKSwNCiAgICAgICAgICBjYXBhYmlsaXRpZXM6IFsNCiAgICAgICAgICAgIHsgbGFiZWw6ICLmt7HluqbmgJ3ogIMiLCB2YWx1ZTogImRlZXBfdGhpbmtpbmciIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAi6IGU572R5pCc57SiIiwgdmFsdWU6ICJ3ZWJfc2VhcmNoIiB9LA0KICAgICAgICAgIF0sDQogICAgICAgICAgc2VsZWN0ZWRDYXBhYmlsaXRpZXM6IFsiZGVlcF90aGlua2luZyIsICJ3ZWJfc2VhcmNoIl0sDQogICAgICAgICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgICBzdGF0dXM6ICJpZGxlIiwNCiAgICAgICAgICBwcm9ncmVzc0xvZ3M6IFtdLA0KICAgICAgICAgIGlzRXhwYW5kZWQ6IHRydWUsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAi6LGG5YyFIiwNCiAgICAgICAgICBhdmF0YXI6IHJlcXVpcmUoIi4uLy4uLy4uL2Fzc2V0cy9haS/osYbljIUucG5nIiksDQogICAgICAgICAgY2FwYWJpbGl0aWVzOiBbeyBsYWJlbDogIua3seW6puaAneiAgyIsIHZhbHVlOiAiZGVlcF90aGlua2luZyIgfV0sDQogICAgICAgICAgc2VsZWN0ZWRDYXBhYmlsaXRpZXM6IFsiZGVlcF90aGlua2luZyJdLA0KICAgICAgICAgIGVuYWJsZWQ6IHRydWUsDQogICAgICAgICAgc3RhdHVzOiAiaWRsZSIsDQogICAgICAgICAgcHJvZ3Jlc3NMb2dzOiBbXSwNCiAgICAgICAgICBpc0V4cGFuZGVkOiB0cnVlLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogIk1pbmlNYXggQ2hhdCIsDQogICAgICAgICAgYXZhdGFyOiByZXF1aXJlKCIuLi8uLi8uLi9hc3NldHMvYWkvTWluaU1heC5wbmciKSwNCiAgICAgICAgICBjYXBhYmlsaXRpZXM6IFsNCiAgICAgICAgICAgIHsgbGFiZWw6ICLmt7HluqbmgJ3ogIMiLCB2YWx1ZTogImRlZXBfdGhpbmtpbmciIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAi6IGU572R5pCc57SiIiwgdmFsdWU6ICJ3ZWJfc2VhcmNoIiB9LA0KICAgICAgICAgIF0sDQogICAgICAgICAgc2VsZWN0ZWRDYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICAgIGVuYWJsZWQ6IHRydWUsDQogICAgICAgICAgc3RhdHVzOiAiaWRsZSIsDQogICAgICAgICAgcHJvZ3Jlc3NMb2dzOiBbXSwNCiAgICAgICAgICBpc0V4cGFuZGVkOiB0cnVlLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+mAmuS5ieWNg+mXricsDQogICAgICAgICAgYXZhdGFyOiByZXF1aXJlKCcuLi8uLi8uLi9hc3NldHMvYWkvcXcucG5nJyksDQogICAgICAgICAgY2FwYWJpbGl0aWVzOiBbDQogICAgICAgICAgICB7IGxhYmVsOiAn5rex5bqm5oCd6ICDJywgdmFsdWU6ICdkZWVwX3RoaW5raW5nJyB9LA0KICAgICAgICAgICAgeyBsYWJlbDogJ+iBlOe9keaQnOe0oicsIHZhbHVlOiAnd2ViX3NlYXJjaCcgfQ0KICAgICAgICAgIF0sDQogICAgICAgICAgc2VsZWN0ZWRDYXBhYmlsaXR5OiAnJywNCiAgICAgICAgICBlbmFibGVkOiB0cnVlLA0KICAgICAgICAgIHN0YXR1czogJ2lkbGUnLA0KICAgICAgICAgIHByb2dyZXNzTG9nczogW10sDQogICAgICAgICAgaXNFeHBhbmRlZDogdHJ1ZQ0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgcHJvbXB0SW5wdXQ6ICIiLA0KICAgICAgdGFza1N0YXJ0ZWQ6IGZhbHNlLA0KICAgICAgYXV0b1BsYXk6IGZhbHNlLA0KICAgICAgc2NyZWVuc2hvdHM6IFtdLA0KICAgICAgcmVzdWx0czogW10sDQogICAgICBhY3RpdmVSZXN1bHRUYWI6ICJyZXN1bHQtMCIsDQogICAgICBhY3RpdmVDb2xsYXBzZXM6IFsiYWktc2VsZWN0aW9uIiwgInByb21wdC1pbnB1dCJdLCAvLyDpu5jorqTlsZXlvIDov5nkuKTkuKrljLrln58NCiAgICAgIHNob3dJbWFnZURpYWxvZzogZmFsc2UsDQogICAgICBjdXJyZW50TGFyZ2VJbWFnZTogIiIsDQogICAgICBlbmFibGVkQUlzOiBbXSwNCiAgICAgIHR1cm5kb3duU2VydmljZTogbmV3IFR1cm5kb3duU2VydmljZSh7DQogICAgICAgIGhlYWRpbmdTdHlsZTogImF0eCIsDQogICAgICAgIGNvZGVCbG9ja1N0eWxlOiAiZmVuY2VkIiwNCiAgICAgICAgZW1EZWxpbWl0ZXI6ICIqIiwNCiAgICAgIH0pLA0KICAgICAgc2NvcmVEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHNlbGVjdGVkUmVzdWx0czogW10sDQogICAgICBzY29yZVByb21wdDogYOivt+S9oOa3seW6pumYheivu+S7peS4i+WHoOevh+WGheWuue+8jOS7juWkmuS4que7tOW6pui/m+ihjOmAkOmhueaJk+WIhu+8jOi+k+WHuuivhOWIhue7k+aenOOAguW5tuWcqOS7peS4i+WQhOevh+aWh+eroOeahOWfuuehgOS4iuWNmumHh+S8l+mVv++8jOe7vOWQiOaVtOeQhuS4gOevh+abtOWFqOmdoueahOaWh+eroOOAgmAsDQogICAgICBsYXlvdXREaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGxheW91dFByb21wdDogIiIsDQogICAgICBjdXJyZW50TGF5b3V0UmVzdWx0OiBudWxsLCAvLyDlvZPliY3opoHmjpLniYjnmoTnu5PmnpwNCiAgICAgIGhpc3RvcnlEcmF3ZXJWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGNoYXRIaXN0b3J5OiBbXSwNCiAgICAgIHB1c2hPZmZpY2VOdW06IDAsIC8vIOaKlemAkuWIsOWFrOS8l+WPt+eahOmAkuWinue8luWPtw0KICAgICAgcHVzaGluZ1RvV2VjaGF0OiBmYWxzZSwgLy8g5oqV6YCS5Yiw5YWs5LyX5Y+355qEbG9hZGluZ+eKtuaAgQ0KICAgICAgc2VsZWN0ZWRNZWRpYTogIndlY2hhdCIsIC8vIOm7mOiupOmAieaLqeWFrOS8l+WPtw0KICAgICAgcHVzaGluZ1RvTWVkaWE6IGZhbHNlLCAvLyDmipXpgJLliLDlqpLkvZPnmoRsb2FkaW5n54q25oCBDQogICAgICAvLyDlvq7lpLTmnaHnm7jlhbPlj5jph48NCiAgICAgIHR0aEZsb3dWaXNpYmxlOiBmYWxzZSwgLy8g5b6u5aS05p2h5Y+R5biD5rWB56iL5by556qXDQogICAgICB0dGhGbG93TG9nczogW10sIC8vIOW+ruWktOadoeWPkeW4g+a1geeoi+aXpeW/lw0KICAgICAgdHRoRmxvd0ltYWdlczogW10sIC8vIOW+ruWktOadoeWPkeW4g+a1geeoi+WbvueJhw0KICAgICAgdHRoQXJ0aWNsZUVkaXRWaXNpYmxlOiBmYWxzZSwgLy8g5b6u5aS05p2h5paH56ug57yW6L6R5by556qXDQogICAgICB0dGhBcnRpY2xlVGl0bGU6ICcnLCAvLyDlvq7lpLTmnaHmlofnq6DmoIfpopgNCiAgICAgIHR0aEFydGljbGVDb250ZW50OiAnJywgLy8g5b6u5aS05p2h5paH56ug5YaF5a65DQogICAgfTsNCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBjYW5TZW5kKCkgew0KICAgICAgcmV0dXJuICgNCiAgICAgICAgdGhpcy5wcm9tcHRJbnB1dC50cmltKCkubGVuZ3RoID4gMCAmJg0KICAgICAgICB0aGlzLmFpTGlzdC5zb21lKChhaSkgPT4gYWkuZW5hYmxlZCkNCiAgICAgICk7DQogICAgfSwNCiAgICBjYW5TY29yZSgpIHsNCiAgICAgIHJldHVybiAoDQogICAgICAgIHRoaXMuc2VsZWN0ZWRSZXN1bHRzLmxlbmd0aCA+IDAgJiYgdGhpcy5zY29yZVByb21wdC50cmltKCkubGVuZ3RoID4gMA0KICAgICAgKTsNCiAgICB9LA0KICAgIGNhbkxheW91dCgpIHsNCiAgICAgIHJldHVybiB0aGlzLmxheW91dFByb21wdC50cmltKCkubGVuZ3RoID4gMDsNCiAgICB9LA0KICAgIGdyb3VwZWRIaXN0b3J5KCkgew0KICAgICAgY29uc3QgZ3JvdXBzID0ge307DQogICAgICBjb25zdCBjaGF0R3JvdXBzID0ge307DQoNCiAgICAgIC8vIOmmluWFiOaMiWNoYXRJZOWIhue7hA0KICAgICAgdGhpcy5jaGF0SGlzdG9yeS5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgIGlmICghY2hhdEdyb3Vwc1tpdGVtLmNoYXRJZF0pIHsNCiAgICAgICAgICBjaGF0R3JvdXBzW2l0ZW0uY2hhdElkXSA9IFtdOw0KICAgICAgICB9DQogICAgICAgIGNoYXRHcm91cHNbaXRlbS5jaGF0SWRdLnB1c2goaXRlbSk7DQogICAgICB9KTsNCg0KICAgICAgLy8g54S25ZCO5oyJ5pel5pyf5YiG57uE77yM5bm25aSE55CG54i25a2Q5YWz57O7DQogICAgICBPYmplY3QudmFsdWVzKGNoYXRHcm91cHMpLmZvckVhY2goKGNoYXRHcm91cCkgPT4gew0KICAgICAgICAvLyDmjInml7bpl7TmjpLluo8NCiAgICAgICAgY2hhdEdyb3VwLnNvcnQoDQogICAgICAgICAgKGEsIGIpID0+IG5ldyBEYXRlKGEuY3JlYXRlVGltZSkgLSBuZXcgRGF0ZShiLmNyZWF0ZVRpbWUpDQogICAgICAgICk7DQoNCiAgICAgICAgLy8g6I635Y+W5pyA5pep55qE6K6w5b2V5L2c5Li654i257qnDQogICAgICAgIGNvbnN0IHBhcmVudEl0ZW0gPSBjaGF0R3JvdXBbMF07DQogICAgICAgIGNvbnN0IGRhdGUgPSB0aGlzLmdldEhpc3RvcnlEYXRlKHBhcmVudEl0ZW0uY3JlYXRlVGltZSk7DQoNCiAgICAgICAgaWYgKCFncm91cHNbZGF0ZV0pIHsNCiAgICAgICAgICBncm91cHNbZGF0ZV0gPSBbXTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOa3u+WKoOeItue6p+iusOW9lQ0KICAgICAgICBncm91cHNbZGF0ZV0ucHVzaCh7DQogICAgICAgICAgLi4ucGFyZW50SXRlbSwNCiAgICAgICAgICBpc1BhcmVudDogdHJ1ZSwNCiAgICAgICAgICBpc0V4cGFuZGVkOiB0aGlzLmV4cGFuZGVkSGlzdG9yeUl0ZW1zW3BhcmVudEl0ZW0uY2hhdElkXSB8fCBmYWxzZSwNCiAgICAgICAgICBjaGlsZHJlbjogY2hhdEdyb3VwLnNsaWNlKDEpLm1hcCgoY2hpbGQpID0+ICh7DQogICAgICAgICAgICAuLi5jaGlsZCwNCiAgICAgICAgICAgIGlzUGFyZW50OiBmYWxzZSwNCiAgICAgICAgICB9KSksDQogICAgICAgIH0pOw0KICAgICAgfSk7DQoNCiAgICAgIHJldHVybiBncm91cHM7DQogICAgfSwNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICBjb25zb2xlLmxvZygi6aG16Z2i5Yid5aeL5YyWIC0gdXNlcklkOiIsIHRoaXMudXNlcklkKTsNCiAgICBjb25zb2xlLmxvZygi6aG16Z2i5Yid5aeL5YyWIC0gY29ycElkOiIsIHRoaXMuY29ycElkKTsNCiAgICB0aGlzLmluaXRXZWJTb2NrZXQodGhpcy51c2VySWQpOw0KICAgIGNvbnNvbGUubG9nKCLlvIDlp4vliqDovb3ljoblj7LorrDlvZUuLi4iKTsNCiAgICB0aGlzLmxvYWRDaGF0SGlzdG9yeSgwKTsgLy8g5Yqg6L295Y6G5Y+y6K6w5b2VDQogICAgY29uc29sZS5sb2coIuW8gOWni+WKoOi9veS4iuasoeS8muivnS4uLiIpOw0KICAgIHRoaXMubG9hZExhc3RDaGF0KCk7IC8vIOWKoOi9veS4iuasoeS8muivnQ0KICB9LA0KICB3YXRjaDogew0KICAgIC8vIOebkeWQrOWqkuS9k+mAieaLqeWPmOWMlu+8jOiHquWKqOWKoOi9veWvueW6lOeahOaPkOekuuivjQ0KICAgIHNlbGVjdGVkTWVkaWE6IHsNCiAgICAgIGhhbmRsZXIobmV3TWVkaWEpIHsNCiAgICAgICAgdGhpcy5sb2FkTWVkaWFQcm9tcHQobmV3TWVkaWEpOw0KICAgICAgfSwNCiAgICAgIGltbWVkaWF0ZTogZmFsc2UNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBzZW5kUHJvbXB0KCkgew0KICAgICAgaWYgKCF0aGlzLmNhblNlbmQpIHJldHVybjsNCg0KICAgICAgdGhpcy5zY3JlZW5zaG90cyA9IFtdOw0KICAgICAgLy8g5oqY5Y+g5omA5pyJ5Yy65Z+fDQogICAgICB0aGlzLmFjdGl2ZUNvbGxhcHNlcyA9IFtdOw0KDQogICAgICB0aGlzLnRhc2tTdGFydGVkID0gdHJ1ZTsNCiAgICAgIHRoaXMucmVzdWx0cyA9IFtdOyAvLyDmuIXnqbrkuYvliY3nmoTnu5PmnpwNCg0KICAgICAgdGhpcy51c2VySW5mb1JlcS5yb2xlcyA9ICIiOw0KDQogICAgICB0aGlzLnVzZXJJbmZvUmVxLnRhc2tJZCA9IHV1aWR2NCgpOw0KICAgICAgdGhpcy51c2VySW5mb1JlcS51c2VySWQgPSB0aGlzLnVzZXJJZDsNCiAgICAgIHRoaXMudXNlckluZm9SZXEuY29ycElkID0gdGhpcy5jb3JwSWQ7DQogICAgICB0aGlzLnVzZXJJbmZvUmVxLnVzZXJQcm9tcHQgPSB0aGlzLnByb21wdElucHV0Ow0KDQogICAgICAvLyDojrflj5blkK/nlKjnmoRBSeWIl+ihqOWPiuWFtueKtuaAgQ0KICAgICAgdGhpcy5lbmFibGVkQUlzID0gdGhpcy5haUxpc3QuZmlsdGVyKChhaSkgPT4gYWkuZW5hYmxlZCk7DQoNCiAgICAgIC8vIOWwhuaJgOacieWQr+eUqOeahEFJ54q25oCB6K6+572u5Li66L+Q6KGM5LitDQogICAgICB0aGlzLmVuYWJsZWRBSXMuZm9yRWFjaCgoYWkpID0+IHsNCiAgICAgICAgdGhpcy4kc2V0KGFpLCAic3RhdHVzIiwgInJ1bm5pbmciKTsNCiAgICAgIH0pOw0KDQogICAgICB0aGlzLmVuYWJsZWRBSXMuZm9yRWFjaCgoYWkpID0+IHsNCiAgICAgICAgaWYgKGFpLm5hbWUgPT09ICJEZWVwU2VlayIgJiYgYWkuZW5hYmxlZCkgew0KICAgICAgICAgIHRoaXMudXNlckluZm9SZXEucm9sZXMgPSB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzICsgImRlZXBzZWVrLCI7DQogICAgICAgICAgaWYgKGFpLnNlbGVjdGVkQ2FwYWJpbGl0aWVzLmluY2x1ZGVzKCJkZWVwX3RoaW5raW5nIikpIHsNCiAgICAgICAgICAgIHRoaXMudXNlckluZm9SZXEucm9sZXMgPSB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzICsgImRzLXNkc2ssIjsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKGFpLnNlbGVjdGVkQ2FwYWJpbGl0aWVzLmluY2x1ZGVzKCJ3ZWJfc2VhcmNoIikpIHsNCiAgICAgICAgICAgIHRoaXMudXNlckluZm9SZXEucm9sZXMgPSB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzICsgImRzLWx3c3MsIjsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgaWYgKGFpLm5hbWUgPT09ICLosYbljIUiKSB7DQogICAgICAgICAgdGhpcy51c2VySW5mb1JlcS5yb2xlcyA9IHRoaXMudXNlckluZm9SZXEucm9sZXMgKyAiemotZGIsIjsNCiAgICAgICAgICBpZiAoYWkuc2VsZWN0ZWRDYXBhYmlsaXRpZXMuaW5jbHVkZXMoImRlZXBfdGhpbmtpbmciKSkgew0KICAgICAgICAgICAgdGhpcy51c2VySW5mb1JlcS5yb2xlcyA9IHRoaXMudXNlckluZm9SZXEucm9sZXMgKyAiemotZGItc2RzaywiOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBpZiAoYWkubmFtZSA9PT0gIk1pbmlNYXggQ2hhdCIpIHsNCiAgICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzID0gdGhpcy51c2VySW5mb1JlcS5yb2xlcyArICJtaW5pLW1heC1hZ2VudCwiOw0KICAgICAgICAgIGlmIChhaS5zZWxlY3RlZENhcGFiaWxpdGllcy5pbmNsdWRlcygiZGVlcF90aGlua2luZyIpKSB7DQogICAgICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzID0gdGhpcy51c2VySW5mb1JlcS5yb2xlcyArICJtYXgtc2RzaywiOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAoYWkuc2VsZWN0ZWRDYXBhYmlsaXRpZXMuaW5jbHVkZXMoIndlYl9zZWFyY2giKSkgew0KICAgICAgICAgICAgdGhpcy51c2VySW5mb1JlcS5yb2xlcyA9IHRoaXMudXNlckluZm9SZXEucm9sZXMgKyAibWF4LWx3c3MsIjsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgaWYoYWkubmFtZSA9PT0gJ+mAmuS5ieWNg+mXricgJiYgYWkuZW5hYmxlZCl7DQogICAgICAgICAgdGhpcy51c2VySW5mb1JlcS5yb2xlcyA9IHRoaXMudXNlckluZm9SZXEucm9sZXMgKyAndHktcXcsJzsNCiAgICAgICAgICBpZiAoYWkuc2VsZWN0ZWRDYXBhYmlsaXR5LmluY2x1ZGVzKCJkZWVwX3RoaW5raW5nIikpIHsNCiAgICAgICAgICAgIHRoaXMudXNlckluZm9SZXEucm9sZXMgPSB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzICsgJ3R5LXF3LXNkc2ssJw0KICAgICAgICAgIH0gZWxzZSBpZiAoYWkuc2VsZWN0ZWRDYXBhYmlsaXR5LmluY2x1ZGVzKCJ3ZWJfc2VhcmNoIikpIHsNCiAgICAgICAgICAgIHRoaXMudXNlckluZm9SZXEucm9sZXMgPSB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzICsgJ3R5LXF3LWx3c3MsJzsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KDQogICAgICBjb25zb2xlLmxvZygi5Y+C5pWw77yaIiwgdGhpcy51c2VySW5mb1JlcSk7DQoNCiAgICAgIC8v6LCD55So5ZCO56uv5o6l5Y+jDQogICAgICB0aGlzLmpzb25ScGNSZXFlc3QubWV0aG9kID0gIuS9v+eUqEY4UyI7DQogICAgICB0aGlzLmpzb25ScGNSZXFlc3QucGFyYW1zID0gdGhpcy51c2VySW5mb1JlcTsNCiAgICAgIHRoaXMubWVzc2FnZSh0aGlzLmpzb25ScGNSZXFlc3QpOw0KICAgICAgdGhpcy51c2VySW5mb1JlcS5pc05ld0NoYXQgPSBmYWxzZTsNCiAgICB9LA0KDQogICAgbWVzc2FnZShkYXRhKSB7DQogICAgICBtZXNzYWdlKGRhdGEpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAxKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubWVzc2FnZXMgfHwgJ+aTjeS9nOWksei0pScpOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaCgoZXJyb3IpID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcigiTWVzc2FnZSBBUEnosIPnlKjlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICAvLyDov5nph4zkuI3lho3mmL7npLrplJnor6/mtojmga/vvIzlm6DkuLrlhajlsYDmi6bmiKrlmajlt7Lnu4/lpITnkIbkuoYNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5aSE55CG6YCa5LmJ5Y2V6YCJ6YC76L6RDQogICAgc2VsZWN0U2luZ2xlQ2FwYWJpbGl0eShhaSwgY2FwYWJpbGl0eVZhbHVlKSB7DQogICAgICBpZiAoIWFpLmVuYWJsZWQpIHJldHVybjsNCg0KICAgICAgaWYgKGFpLnNlbGVjdGVkQ2FwYWJpbGl0eSA9PT0gY2FwYWJpbGl0eVZhbHVlKSB7DQogICAgICAgIHRoaXMuJHNldChhaSwgJ3NlbGVjdGVkQ2FwYWJpbGl0eScsICcnKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJHNldChhaSwgJ3NlbGVjdGVkQ2FwYWJpbGl0eScsIGNhcGFiaWxpdHlWYWx1ZSk7DQogICAgICB9DQogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpOw0KICAgIH0sDQogICAgdG9nZ2xlQ2FwYWJpbGl0eShhaSwgY2FwYWJpbGl0eVZhbHVlKSB7DQogICAgICBpZiAoIWFpLmVuYWJsZWQpIHJldHVybjsNCg0KICAgICAgY29uc3QgaW5kZXggPSBhaS5zZWxlY3RlZENhcGFiaWxpdGllcy5pbmRleE9mKGNhcGFiaWxpdHlWYWx1ZSk7DQogICAgICBjb25zb2xlLmxvZygi5YiH5o2i5YmNOiIsIGFpLnNlbGVjdGVkQ2FwYWJpbGl0aWVzKTsNCiAgICAgIGlmIChpbmRleCA9PT0gLTEpIHsNCiAgICAgICAgLy8g5aaC5p6c5LiN5a2Y5Zyo77yM5YiZ5re75YqgDQogICAgICAgIHRoaXMuJHNldCgNCiAgICAgICAgICBhaS5zZWxlY3RlZENhcGFiaWxpdGllcywNCiAgICAgICAgICBhaS5zZWxlY3RlZENhcGFiaWxpdGllcy5sZW5ndGgsDQogICAgICAgICAgY2FwYWJpbGl0eVZhbHVlDQogICAgICAgICk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDlpoLmnpzlt7LlrZjlnKjvvIzliJnnp7vpmaQNCiAgICAgICAgY29uc3QgbmV3Q2FwYWJpbGl0aWVzID0gWy4uLmFpLnNlbGVjdGVkQ2FwYWJpbGl0aWVzXTsNCiAgICAgICAgbmV3Q2FwYWJpbGl0aWVzLnNwbGljZShpbmRleCwgMSk7DQogICAgICAgIHRoaXMuJHNldChhaSwgInNlbGVjdGVkQ2FwYWJpbGl0aWVzIiwgbmV3Q2FwYWJpbGl0aWVzKTsNCiAgICAgIH0NCiAgICAgIGNvbnNvbGUubG9nKCLliIfmjaLlkI46IiwgYWkuc2VsZWN0ZWRDYXBhYmlsaXRpZXMpOw0KICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsgLy8g5by65Yi25pu05paw6KeG5Zu+DQogICAgfSwNCiAgICBnZXRTdGF0dXNUZXh0KHN0YXR1cykgew0KICAgICAgc3dpdGNoIChzdGF0dXMpIHsNCiAgICAgICAgY2FzZSAiaWRsZSI6DQogICAgICAgICAgcmV0dXJuICLnrYnlvoXkuK0iOw0KICAgICAgICBjYXNlICJydW5uaW5nIjoNCiAgICAgICAgICByZXR1cm4gIuato+WcqOaJp+ihjCI7DQogICAgICAgIGNhc2UgImNvbXBsZXRlZCI6DQogICAgICAgICAgcmV0dXJuICLlt7LlrozmiJAiOw0KICAgICAgICBjYXNlICJmYWlsZWQiOg0KICAgICAgICAgIHJldHVybiAi5omn6KGM5aSx6LSlIjsNCiAgICAgICAgZGVmYXVsdDoNCiAgICAgICAgICByZXR1cm4gIuacquefpeeKtuaAgSI7DQogICAgICB9DQogICAgfSwNCiAgICBnZXRTdGF0dXNJY29uKHN0YXR1cykgew0KICAgICAgc3dpdGNoIChzdGF0dXMpIHsNCiAgICAgICAgY2FzZSAiaWRsZSI6DQogICAgICAgICAgcmV0dXJuICJlbC1pY29uLXRpbWUiOw0KICAgICAgICBjYXNlICJydW5uaW5nIjoNCiAgICAgICAgICByZXR1cm4gImVsLWljb24tbG9hZGluZyI7DQogICAgICAgIGNhc2UgImNvbXBsZXRlZCI6DQogICAgICAgICAgcmV0dXJuICJlbC1pY29uLWNoZWNrIHN1Y2Nlc3MtaWNvbiI7DQogICAgICAgIGNhc2UgImZhaWxlZCI6DQogICAgICAgICAgcmV0dXJuICJlbC1pY29uLWNsb3NlIGVycm9yLWljb24iOw0KICAgICAgICBkZWZhdWx0Og0KICAgICAgICAgIHJldHVybiAiZWwtaWNvbi1xdWVzdGlvbiI7DQogICAgICB9DQogICAgfSwNCiAgICByZW5kZXJNYXJrZG93bih0ZXh0KSB7DQogICAgICByZXR1cm4gbWFya2VkKHRleHQpOw0KICAgIH0sDQogICAgLy8gSFRNTOi9rOe6r+aWh+acrA0KICAgIGh0bWxUb1RleHQoaHRtbCkgew0KICAgICAgY29uc3QgdGVtcERpdiA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoImRpdiIpOw0KICAgICAgdGVtcERpdi5pbm5lckhUTUwgPSBodG1sOw0KICAgICAgcmV0dXJuIHRlbXBEaXYudGV4dENvbnRlbnQgfHwgdGVtcERpdi5pbm5lclRleHQgfHwgIiI7DQogICAgfSwNCg0KICAgIC8vIEhUTUzovaxNYXJrZG93bg0KICAgIGh0bWxUb01hcmtkb3duKGh0bWwpIHsNCiAgICAgIHJldHVybiB0aGlzLnR1cm5kb3duU2VydmljZS50dXJuZG93bihodG1sKTsNCiAgICB9LA0KDQogICAgY29weVJlc3VsdChjb250ZW50KSB7DQogICAgICAvLyDlsIZIVE1M6L2s5o2i5Li657qv5paH5pysDQogICAgICBjb25zdCBwbGFpblRleHQgPSB0aGlzLmh0bWxUb1RleHQoY29udGVudCk7DQogICAgICBjb25zdCB0ZXh0YXJlYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoInRleHRhcmVhIik7DQogICAgICB0ZXh0YXJlYS52YWx1ZSA9IHBsYWluVGV4dDsNCiAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQodGV4dGFyZWEpOw0KICAgICAgdGV4dGFyZWEuc2VsZWN0KCk7DQogICAgICBkb2N1bWVudC5leGVjQ29tbWFuZCgiY29weSIpOw0KICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZCh0ZXh0YXJlYSk7DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuW3suWkjeWItue6r+aWh+acrOWIsOWJqui0tOadvyIpOw0KICAgIH0sDQoNCiAgICBleHBvcnRSZXN1bHQocmVzdWx0KSB7DQogICAgICAvLyDlsIZIVE1M6L2s5o2i5Li6TWFya2Rvd24NCiAgICAgIGNvbnN0IG1hcmtkb3duID0gcmVzdWx0LmNvbnRlbnQ7DQogICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW21hcmtkb3duXSwgeyB0eXBlOiAidGV4dC9tYXJrZG93biIgfSk7DQogICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgiYSIpOw0KICAgICAgbGluay5ocmVmID0gVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKTsNCiAgICAgIGxpbmsuZG93bmxvYWQgPSBgJHtyZXN1bHQuYWlOYW1lfV/nu5PmnpxfJHtuZXcgRGF0ZSgpDQogICAgICAgIC50b0lTT1N0cmluZygpDQogICAgICAgIC5zbGljZSgwLCAxMCl9Lm1kYDsNCiAgICAgIGxpbmsuY2xpY2soKTsNCiAgICAgIFVSTC5yZXZva2VPYmplY3RVUkwobGluay5ocmVmKTsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5bey5a+85Ye6TWFya2Rvd27mlofku7YiKTsNCiAgICB9LA0KDQogICAgb3BlblNoYXJlVXJsKHNoYXJlVXJsKSB7DQogICAgICBpZiAoc2hhcmVVcmwpIHsNCiAgICAgICAgd2luZG93Lm9wZW4oc2hhcmVVcmwsICJfYmxhbmsiKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi5pqC5peg5Y6f6ZO+5o6lIik7DQogICAgICB9DQogICAgfSwNCiAgICBzaG93TGFyZ2VJbWFnZShpbWFnZVVybCkgew0KICAgICAgdGhpcy5jdXJyZW50TGFyZ2VJbWFnZSA9IGltYWdlVXJsOw0KICAgICAgdGhpcy5zaG93SW1hZ2VEaWFsb2cgPSB0cnVlOw0KICAgICAgLy8g5om+5Yiw5b2T5YmN5Zu+54mH55qE57Si5byV77yM6K6+572u6L2u5pKt5Zu+55qE5Yid5aeL5L2N572uDQogICAgICBjb25zdCBjdXJyZW50SW5kZXggPSB0aGlzLnNjcmVlbnNob3RzLmluZGV4T2YoaW1hZ2VVcmwpOw0KICAgICAgaWYgKGN1cnJlbnRJbmRleCAhPT0gLTEpIHsNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIGNvbnN0IGNhcm91c2VsID0gdGhpcy4kZWwucXVlcnlTZWxlY3RvcigiLmltYWdlLWRpYWxvZyAuZWwtY2Fyb3VzZWwiKTsNCiAgICAgICAgICBpZiAoY2Fyb3VzZWwgJiYgY2Fyb3VzZWwuX192dWVfXykgew0KICAgICAgICAgICAgY2Fyb3VzZWwuX192dWVfXy5zZXRBY3RpdmVJdGVtKGN1cnJlbnRJbmRleCk7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGNsb3NlTGFyZ2VJbWFnZSgpIHsNCiAgICAgIHRoaXMuc2hvd0ltYWdlRGlhbG9nID0gZmFsc2U7DQogICAgICB0aGlzLmN1cnJlbnRMYXJnZUltYWdlID0gIiI7DQogICAgfSwNCiAgICAvLyBXZWJTb2NrZXQg55u45YWz5pa55rOVDQogICAgaW5pdFdlYlNvY2tldChpZCkgew0KICAgICAgY29uc3Qgd3NVcmwgPSBwcm9jZXNzLmVudi5WVUVfQVBQX1dTX0FQSSArIGBteXBjLSR7aWR9YDsNCiAgICAgIGNvbnNvbGUubG9nKCJXZWJTb2NrZXQgVVJMOiIsIHByb2Nlc3MuZW52LlZVRV9BUFBfV1NfQVBJKTsNCiAgICAgIHdlYnNvY2tldENsaWVudC5jb25uZWN0KHdzVXJsLCAoZXZlbnQpID0+IHsNCiAgICAgICAgc3dpdGNoIChldmVudC50eXBlKSB7DQogICAgICAgICAgY2FzZSAib3BlbiI6DQogICAgICAgICAgICAvLyB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJycpOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAibWVzc2FnZSI6DQogICAgICAgICAgICB0aGlzLmhhbmRsZVdlYlNvY2tldE1lc3NhZ2UoZXZlbnQuZGF0YSk7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlICJjbG9zZSI6DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIldlYlNvY2tldOi/nuaOpeW3suWFs+mXrSIpOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAiZXJyb3IiOg0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigiV2ViU29ja2V06L+e5o6l6ZSZ6K+vIik7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlICJyZWNvbm5lY3RfZmFpbGVkIjoNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIldlYlNvY2tldOmHjei/nuWksei0pe+8jOivt+WIt+aWsOmhtemdoumHjeivlSIpOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICBoYW5kbGVXZWJTb2NrZXRNZXNzYWdlKGRhdGEpIHsNCiAgICAgIGNvbnN0IGRhdGFzdHIgPSBkYXRhOw0KICAgICAgY29uc3QgZGF0YU9iaiA9IEpTT04ucGFyc2UoZGF0YXN0cik7DQoNCiAgICAgIC8vIOWkhOeQhmNoYXRJZOa2iOaBrw0KICAgICAgaWYgKGRhdGFPYmoudHlwZSA9PT0gIlJFVFVSTl9ZQlQxX0NIQVRJRCIgJiYgZGF0YU9iai5jaGF0SWQpIHsNCiAgICAgICAgdGhpcy51c2VySW5mb1JlcS50b25lQ2hhdElkID0gZGF0YU9iai5jaGF0SWQ7DQogICAgICB9IGVsc2UgaWYgKGRhdGFPYmoudHlwZSA9PT0gIlJFVFVSTl9ZQkRTX0NIQVRJRCIgJiYgZGF0YU9iai5jaGF0SWQpIHsNCiAgICAgICAgdGhpcy51c2VySW5mb1JlcS55YkRzQ2hhdElkID0gZGF0YU9iai5jaGF0SWQ7DQogICAgICB9IGVsc2UgaWYgKGRhdGFPYmoudHlwZSA9PT0gIlJFVFVSTl9EQl9DSEFUSUQiICYmIGRhdGFPYmouY2hhdElkKSB7DQogICAgICAgIHRoaXMudXNlckluZm9SZXEuZGJDaGF0SWQgPSBkYXRhT2JqLmNoYXRJZDsNCiAgICAgIH0gZWxzZSBpZiAoZGF0YU9iai50eXBlID09PSAnUkVUVVJOX1RZX0NIQVRJRCcgJiYgZGF0YU9iai5jaGF0SWQpIHsNCiAgICAgICAgdGhpcy51c2VySW5mb1JlcS50eUNoYXRJZCA9IGRhdGFPYmouY2hhdElkOw0KICAgICAgfSBlbHNlIGlmIChkYXRhT2JqLnR5cGUgPT09ICJSRVRVUk5fTUFYX0NIQVRJRCIgJiYgZGF0YU9iai5jaGF0SWQpIHsNCiAgICAgICAgdGhpcy51c2VySW5mb1JlcS5tYXhDaGF0SWQgPSBkYXRhT2JqLmNoYXRJZDsNCiAgICAgIH0NCg0KICAgICAgLy8g5aSE55CG6L+b5bqm5pel5b+X5raI5oGvDQogICAgICBpZiAoZGF0YU9iai50eXBlID09PSAiUkVUVVJOX1BDX1RBU0tfTE9HIiAmJiBkYXRhT2JqLmFpTmFtZSkgew0KICAgICAgICBjb25zdCB0YXJnZXRBSSA9IHRoaXMuZW5hYmxlZEFJcy5maW5kKA0KICAgICAgICAgIChhaSkgPT4gYWkubmFtZSA9PT0gZGF0YU9iai5haU5hbWUNCiAgICAgICAgKTsNCiAgICAgICAgaWYgKHRhcmdldEFJKSB7DQogICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5bey5a2Y5Zyo55u45ZCM5YaF5a6555qE5pel5b+X77yM6YG/5YWN6YeN5aSN5re75YqgDQogICAgICAgICAgY29uc3QgZXhpc3RpbmdMb2cgPSB0YXJnZXRBSS5wcm9ncmVzc0xvZ3MuZmluZChsb2cgPT4gbG9nLmNvbnRlbnQgPT09IGRhdGFPYmouY29udGVudCk7DQogICAgICAgICAgaWYgKCFleGlzdGluZ0xvZykgew0KICAgICAgICAgICAgLy8g5bCG5paw6L+b5bqm5re75Yqg5Yiw5pWw57uE5byA5aS0DQogICAgICAgICAgICB0YXJnZXRBSS5wcm9ncmVzc0xvZ3MudW5zaGlmdCh7DQogICAgICAgICAgICAgIGNvbnRlbnQ6IGRhdGFPYmouY29udGVudCwNCiAgICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLA0KICAgICAgICAgICAgICBpc0NvbXBsZXRlZDogZmFsc2UsDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgLy8g5aSE55CG55+l5LmO5oqV6YCS5Lu75Yqh5pel5b+XDQogICAgICBpZiAoZGF0YU9iai50eXBlID09PSAiUkVUVVJOX01FRElBX1RBU0tfTE9HIiAmJiBkYXRhT2JqLmFpTmFtZSA9PT0gIuaKlemAkuWIsOefpeS5jiIpIHsNCiAgICAgICAgY29uc3QgemhpaHVBSSA9IHRoaXMuZW5hYmxlZEFJcy5maW5kKChhaSkgPT4gYWkubmFtZSA9PT0gIuaKlemAkuWIsOefpeS5jiIpOw0KICAgICAgICBpZiAoemhpaHVBSSkgew0KICAgICAgICAgIC8vIOajgOafpeaYr+WQpuW3suWtmOWcqOebuOWQjOWGheWuueeahOaXpeW/l++8jOmBv+WFjemHjeWkjea3u+WKoA0KICAgICAgICAgIGNvbnN0IGV4aXN0aW5nTG9nID0gemhpaHVBSS5wcm9ncmVzc0xvZ3MuZmluZChsb2cgPT4gbG9nLmNvbnRlbnQgPT09IGRhdGFPYmouY29udGVudCk7DQogICAgICAgICAgaWYgKCFleGlzdGluZ0xvZykgew0KICAgICAgICAgICAgLy8g5bCG5paw6L+b5bqm5re75Yqg5Yiw5pWw57uE5byA5aS0DQogICAgICAgICAgICB6aGlodUFJLnByb2dyZXNzTG9ncy51bnNoaWZ0KHsNCiAgICAgICAgICAgICAgY29udGVudDogZGF0YU9iai5jb250ZW50LA0KICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksDQogICAgICAgICAgICAgIGlzQ29tcGxldGVkOiBmYWxzZSwNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICAvLyDlpITnkIbnmb7lrrblj7fmipXpgJLku7vliqHml6Xlv5cNCiAgICAgIGlmIChkYXRhT2JqLnR5cGUgPT09ICJSRVRVUk5fTUVESUFfVEFTS19MT0ciICYmIGRhdGFPYmouYWlOYW1lID09PSAi5oqV6YCS5Yiw55m+5a625Y+3Iikgew0KICAgICAgICBjb25zdCBiYWlqaWFoYW9BSSA9IHRoaXMuZW5hYmxlZEFJcy5maW5kKChhaSkgPT4gYWkubmFtZSA9PT0gIuaKlemAkuWIsOeZvuWutuWPtyIpOw0KICAgICAgICBpZiAoYmFpamlhaGFvQUkpIHsNCiAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKblt7LlrZjlnKjnm7jlkIzlhoXlrrnnmoTml6Xlv5fvvIzpgb/lhY3ph43lpI3mt7vliqANCiAgICAgICAgICBjb25zdCBleGlzdGluZ0xvZyA9IGJhaWppYWhhb0FJLnByb2dyZXNzTG9ncy5maW5kKGxvZyA9PiBsb2cuY29udGVudCA9PT0gZGF0YU9iai5jb250ZW50KTsNCiAgICAgICAgICBpZiAoIWV4aXN0aW5nTG9nKSB7DQogICAgICAgICAgICAvLyDlsIbmlrDov5vluqbmt7vliqDliLDmlbDnu4TlvIDlpLQNCiAgICAgICAgICAgIGJhaWppYWhhb0FJLnByb2dyZXNzTG9ncy51bnNoaWZ0KHsNCiAgICAgICAgICAgICAgY29udGVudDogZGF0YU9iai5jb250ZW50LA0KICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksDQogICAgICAgICAgICAgIGlzQ29tcGxldGVkOiBmYWxzZSwNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICAvLyDlpITnkIbmiKrlm77mtojmga8NCiAgICAgIGlmIChkYXRhT2JqLnR5cGUgPT09ICJSRVRVUk5fUENfVEFTS19JTUciICYmIGRhdGFPYmoudXJsKSB7DQogICAgICAgIC8vIOWwhuaWsOeahOaIquWbvua3u+WKoOWIsOaVsOe7hOW8gOWktA0KICAgICAgICB0aGlzLnNjcmVlbnNob3RzLnVuc2hpZnQoZGF0YU9iai51cmwpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOWkhOeQhuaZuuiDveivhOWIhue7k+aenA0KICAgICAgaWYgKGRhdGFPYmoudHlwZSA9PT0gIlJFVFVSTl9XS1BGX1JFUyIpIHsNCiAgICAgICAgY29uc3Qgd2twZkFJID0gdGhpcy5lbmFibGVkQUlzLmZpbmQoKGFpKSA9PiBhaS5uYW1lID09PSAi5pm66IO96K+E5YiGIik7DQogICAgICAgIGlmICh3a3BmQUkpIHsNCiAgICAgICAgICB0aGlzLiRzZXQod2twZkFJLCAic3RhdHVzIiwgImNvbXBsZXRlZCIpOw0KICAgICAgICAgIGlmICh3a3BmQUkucHJvZ3Jlc3NMb2dzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIHRoaXMuJHNldCh3a3BmQUkucHJvZ3Jlc3NMb2dzWzBdLCAiaXNDb21wbGV0ZWQiLCB0cnVlKTsNCiAgICAgICAgICB9DQogICAgICAgICAgLy8g5re75Yqg6K+E5YiG57uT5p6c5YiwcmVzdWx0c+acgOWJjemdog0KICAgICAgICAgIHRoaXMucmVzdWx0cy51bnNoaWZ0KHsNCiAgICAgICAgICAgIGFpTmFtZTogIuaZuuiDveivhOWIhiIsDQogICAgICAgICAgICBjb250ZW50OiBkYXRhT2JqLmRyYWZ0Q29udGVudCwNCiAgICAgICAgICAgIHNoYXJlVXJsOiBkYXRhT2JqLnNoYXJlVXJsIHx8ICIiLA0KICAgICAgICAgICAgc2hhcmVJbWdVcmw6IGRhdGFPYmouc2hhcmVJbWdVcmwgfHwgIiIsDQogICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksDQogICAgICAgICAgfSk7DQogICAgICAgICAgdGhpcy5hY3RpdmVSZXN1bHRUYWIgPSAicmVzdWx0LTAiOw0KDQogICAgICAgICAgLy8g5pm66IO96K+E5YiG5a6M5oiQ5pe277yM5YaN5qyh5L+d5a2Y5Y6G5Y+y6K6w5b2VDQogICAgICAgICAgdGhpcy5zYXZlSGlzdG9yeSgpOw0KICAgICAgICB9DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgLy8g5aSE55CG5pm66IO95o6S54mI57uT5p6cDQogICAgICBpZiAoZGF0YU9iai50eXBlID09PSAiUkVUVVJOX1pOUEJfUkVTIikgew0KICAgICAgICBjb25zdCB6bnBiQUkgPSB0aGlzLmVuYWJsZWRBSXMuZmluZCgoYWkpID0+IGFpLm5hbWUgPT09ICLmmbrog73mjpLniYgiKTsNCiAgICAgICAgaWYgKHpucGJBSSkgew0KICAgICAgICAgIHRoaXMuJHNldCh6bnBiQUksICJzdGF0dXMiLCAiY29tcGxldGVkIik7DQogICAgICAgICAgaWYgKHpucGJBSS5wcm9ncmVzc0xvZ3MubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgdGhpcy4kc2V0KHpucGJBSS5wcm9ncmVzc0xvZ3NbMF0sICJpc0NvbXBsZXRlZCIsIHRydWUpOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOebtOaOpeiwg+eUqOaKlemAkuWIsOWFrOS8l+WPt+eahOaWueazle+8jOS4jea3u+WKoOWIsOe7k+aenOWxleekug0KICAgICAgICAgIHRoaXMucHVzaFRvV2VjaGF0V2l0aENvbnRlbnQoZGF0YU9iai5kcmFmdENvbnRlbnQpOw0KDQogICAgICAgICAgLy8g5pm66IO95o6S54mI5a6M5oiQ5pe277yM5L+d5a2Y5Y6G5Y+y6K6w5b2VDQogICAgICAgICAgdGhpcy5zYXZlSGlzdG9yeSgpOw0KICAgICAgICB9DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIC8vIOWkhOeQhuefpeS5juaKlemAkue7k+aenO+8iOeLrOeri+S7u+WKoe+8iQ0KICAgICAgaWYgKGRhdGFPYmoudHlwZSA9PT0gIlJFVFVSTl9aSElIVV9ERUxJVkVSWV9SRVMiKSB7DQogICAgICAgIGNvbnN0IHpoaWh1QUkgPSB0aGlzLmVuYWJsZWRBSXMuZmluZCgoYWkpID0+IGFpLm5hbWUgPT09ICLmipXpgJLliLDnn6XkuY4iKTsNCiAgICAgICAgaWYgKHpoaWh1QUkpIHsNCiAgICAgICAgICB0aGlzLiRzZXQoemhpaHVBSSwgInN0YXR1cyIsICJjb21wbGV0ZWQiKTsNCiAgICAgICAgICBpZiAoemhpaHVBSS5wcm9ncmVzc0xvZ3MubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgdGhpcy4kc2V0KHpoaWh1QUkucHJvZ3Jlc3NMb2dzWzBdLCAiaXNDb21wbGV0ZWQiLCB0cnVlKTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDmt7vliqDlrozmiJDml6Xlv5cNCiAgICAgICAgICB6aGlodUFJLnByb2dyZXNzTG9ncy51bnNoaWZ0KHsNCiAgICAgICAgICAgIGNvbnRlbnQ6ICLnn6XkuY7mipXpgJLlrozmiJDvvIEiICsgKGRhdGFPYmoubWVzc2FnZSB8fCAiIiksDQogICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksDQogICAgICAgICAgICBpc0NvbXBsZXRlZDogdHJ1ZSwNCiAgICAgICAgICB9KTsNCg0KICAgICAgICAgIC8vIOefpeS5juaKlemAkuWujOaIkOaXtu+8jOS/neWtmOWOhuWPsuiusOW9lQ0KICAgICAgICAgIHRoaXMuc2F2ZUhpc3RvcnkoKTsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuefpeS5juaKlemAkuS7u+WKoeWujOaIkO+8gSIpOw0KICAgICAgICB9DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIC8vIOWkhOeQhueZvuWutuWPt+aKlemAkue7k+aenO+8iOeLrOeri+S7u+WKoe+8iQ0KICAgICAgaWYgKGRhdGFPYmoudHlwZSA9PT0gIlJFVFVSTl9CQUlKSUFIQU9fREVMSVZFUllfUkVTIikgew0KICAgICAgICBjb25zdCBiYWlqaWFoYW9BSSA9IHRoaXMuZW5hYmxlZEFJcy5maW5kKChhaSkgPT4gYWkubmFtZSA9PT0gIuaKlemAkuWIsOeZvuWutuWPtyIpOw0KICAgICAgICBpZiAoYmFpamlhaGFvQUkpIHsNCiAgICAgICAgICB0aGlzLiRzZXQoYmFpamlhaGFvQUksICJzdGF0dXMiLCAiY29tcGxldGVkIik7DQogICAgICAgICAgaWYgKGJhaWppYWhhb0FJLnByb2dyZXNzTG9ncy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICB0aGlzLiRzZXQoYmFpamlhaGFvQUkucHJvZ3Jlc3NMb2dzWzBdLCAiaXNDb21wbGV0ZWQiLCB0cnVlKTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDmt7vliqDlrozmiJDml6Xlv5cNCiAgICAgICAgICBiYWlqaWFoYW9BSS5wcm9ncmVzc0xvZ3MudW5zaGlmdCh7DQogICAgICAgICAgICBjb250ZW50OiAi55m+5a625Y+35oqV6YCS5a6M5oiQ77yBIiArIChkYXRhT2JqLm1lc3NhZ2UgfHwgIiIpLA0KICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLA0KICAgICAgICAgICAgaXNDb21wbGV0ZWQ6IHRydWUsDQogICAgICAgICAgfSk7DQoNCiAgICAgICAgICAvLyDnmb7lrrblj7fmipXpgJLlrozmiJDml7bvvIzkv53lrZjljoblj7LorrDlvZUNCiAgICAgICAgICB0aGlzLnNhdmVIaXN0b3J5KCk7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLnmb7lrrblj7fmipXpgJLku7vliqHlrozmiJDvvIEiKTsNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOWkhOeQhuW+ruWktOadoeaOkueJiOe7k+aenA0KICAgICAgaWYgKGRhdGFPYmoudHlwZSA9PT0gJ1JFVFVSTl9UVEhfWk5QQl9SRVMnKSB7DQogICAgICAgIC8vIOW+ruWktOadoeaOkueJiEFJ6IqC54K554q25oCB6K6+5Li65bey5a6M5oiQDQogICAgICAgIGNvbnN0IHR0aHBiQUkgPSB0aGlzLmVuYWJsZWRBSXMuZmluZChhaSA9PiBhaS5uYW1lID09PSAn5b6u5aS05p2h5o6S54mIJyk7DQogICAgICAgIGlmICh0dGhwYkFJKSB7DQogICAgICAgICAgdGhpcy4kc2V0KHR0aHBiQUksICdzdGF0dXMnLCAnY29tcGxldGVkJyk7DQogICAgICAgICAgaWYgKHR0aHBiQUkucHJvZ3Jlc3NMb2dzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIHRoaXMuJHNldCh0dGhwYkFJLnByb2dyZXNzTG9nc1swXSwgJ2lzQ29tcGxldGVkJywgdHJ1ZSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHRoaXMudHRoQXJ0aWNsZVRpdGxlID0gZGF0YU9iai50aXRsZSB8fCAnJzsNCiAgICAgICAgdGhpcy50dGhBcnRpY2xlQ29udGVudCA9IGRhdGFPYmouY29udGVudCB8fCAnJzsNCiAgICAgICAgdGhpcy50dGhBcnRpY2xlRWRpdFZpc2libGUgPSB0cnVlOw0KICAgICAgICB0aGlzLnNhdmVIaXN0b3J5KCk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgLy8g5aSE55CG5b6u5aS05p2h5Y+R5biD5rWB56iLDQogICAgICBpZiAoZGF0YU9iai50eXBlID09PSAnUkVUVVJOX1RUSF9GTE9XJykgew0KICAgICAgICAvLyDmt7vliqDmtYHnqIvml6Xlv5cNCiAgICAgICAgaWYgKGRhdGFPYmouY29udGVudCkgew0KICAgICAgICAgIHRoaXMudHRoRmxvd0xvZ3MucHVzaCh7DQogICAgICAgICAgICBjb250ZW50OiBkYXRhT2JqLmNvbnRlbnQsDQogICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksDQogICAgICAgICAgICB0eXBlOiAnZmxvdycsDQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgICAgLy8g5aSE55CG5Zu+54mH5L+h5oGvDQogICAgICAgIGlmIChkYXRhT2JqLnNoYXJlSW1nVXJsKSB7DQogICAgICAgICAgdGhpcy50dGhGbG93SW1hZ2VzLnB1c2goZGF0YU9iai5zaGFyZUltZ1VybCk7DQogICAgICAgIH0NCiAgICAgICAgLy8g56Gu5L+d5rWB56iL5by556qX5pi+56S6DQogICAgICAgIGlmICghdGhpcy50dGhGbG93VmlzaWJsZSkgew0KICAgICAgICAgIHRoaXMudHRoRmxvd1Zpc2libGUgPSB0cnVlOw0KICAgICAgICB9DQogICAgICAgIC8vIOajgOafpeWPkeW4g+e7k+aenA0KICAgICAgICBpZiAoZGF0YU9iai5jb250ZW50ID09PSAnc3VjY2VzcycpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WPkeW4g+WIsOW+ruWktOadoeaIkOWKn++8gScpOw0KICAgICAgICAgIHRoaXMudHRoRmxvd1Zpc2libGUgPSB0cnVlOw0KICAgICAgICB9IGVsc2UgaWYgKGRhdGFPYmouY29udGVudCA9PT0gJ2ZhbHNlJyB8fCBkYXRhT2JqLmNvbnRlbnQgPT09IGZhbHNlKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Y+R5biD5Yiw5b6u5aS05p2h5aSx6LSl77yBJyk7DQogICAgICAgICAgdGhpcy50dGhGbG93VmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMudHRoQXJ0aWNsZUVkaXRWaXNpYmxlID0gdHJ1ZTsNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOWFvOWuueWQjuerr+WPkemAgeeahFJFVFVSTl9QQ19UVEhfSU1H57G75Z6L5Zu+54mH5raI5oGvDQogICAgICBpZiAoZGF0YU9iai50eXBlID09PSAnUkVUVVJOX1BDX1RUSF9JTUcnICYmIGRhdGFPYmoudXJsKSB7DQogICAgICAgIHRoaXMudHRoRmxvd0ltYWdlcy5wdXNoKGRhdGFPYmoudXJsKTsNCiAgICAgICAgaWYgKCF0aGlzLnR0aEZsb3dWaXNpYmxlKSB7DQogICAgICAgICAgdGhpcy50dGhGbG93VmlzaWJsZSA9IHRydWU7DQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDmoLnmja7mtojmga/nsbvlnovmm7TmlrDlr7nlupRBSeeahOeKtuaAgeWSjOe7k+aenA0KICAgICAgbGV0IHRhcmdldEFJID0gbnVsbDsNCiAgICAgIHN3aXRjaCAoZGF0YU9iai50eXBlKSB7DQogICAgICAgIGNhc2UgIlJFVFVSTl9ZQlQxX1JFUyI6DQogICAgICAgIGNhc2UgIlJFVFVSTl9UVVJCT1NfUkVTIjoNCiAgICAgICAgY2FzZSAiUkVUVVJOX1RVUkJPU19MQVJHRV9SRVMiOg0KICAgICAgICBjYXNlICJSRVRVUk5fREVFUFNFRUtfUkVTIjoNCiAgICAgICAgICBjb25zb2xlLmxvZygi5pS25YiwRGVlcFNlZWvmtojmga86IiwgZGF0YU9iaik7DQogICAgICAgICAgdGFyZ2V0QUkgPSB0aGlzLmVuYWJsZWRBSXMuZmluZCgoYWkpID0+IGFpLm5hbWUgPT09ICJEZWVwU2VlayIpOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICJSRVRVUk5fWUJEU19SRVMiOg0KICAgICAgICBjYXNlICJSRVRVUk5fREJfUkVTIjoNCiAgICAgICAgICBjb25zb2xlLmxvZygi5pS25Yiw6LGG5YyF5raI5oGvOiIsIGRhdGFPYmopOw0KICAgICAgICAgIHRhcmdldEFJID0gdGhpcy5lbmFibGVkQUlzLmZpbmQoKGFpKSA9PiBhaS5uYW1lID09PSAi6LGG5YyFIik7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgIlJFVFVSTl9NQVhfUkVTIjoNCiAgICAgICAgICBjb25zb2xlLmxvZygi5pS25YiwTWluaU1heOa2iOaBrzoiLCBkYXRhT2JqKTsNCiAgICAgICAgICB0YXJnZXRBSSA9IHRoaXMuZW5hYmxlZEFJcy5maW5kKChhaSkgPT4gYWkubmFtZSA9PT0gIk1pbmlNYXggQ2hhdCIpOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICdSRVRVUk5fVFlfUkVTJzoNCiAgICAgICAgICBjb25zb2xlLmxvZygn5pS25Yiw6YCa5LmJ5Y2D6Zeu5raI5oGvOicsIGRhdGEpOw0KICAgICAgICAgIHRhcmdldEFJID0gdGhpcy5lbmFibGVkQUlzLmZpbmQoYWkgPT4gYWkubmFtZSA9PT0gJ+mAmuS5ieWNg+mXricpOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgfQ0KDQogICAgICBpZiAodGFyZ2V0QUkpIHsNCiAgICAgICAgLy8g5pu05pawQUnnirbmgIHkuLrlt7LlrozmiJANCiAgICAgICAgdGhpcy4kc2V0KHRhcmdldEFJLCAic3RhdHVzIiwgImNvbXBsZXRlZCIpOw0KDQogICAgICAgIC8vIOWwhuacgOWQjuS4gOadoei/m+W6pua2iOaBr+agh+iusOS4uuW3suWujOaIkA0KICAgICAgICBpZiAodGFyZ2V0QUkucHJvZ3Jlc3NMb2dzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0aGlzLiRzZXQodGFyZ2V0QUkucHJvZ3Jlc3NMb2dzWzBdLCAiaXNDb21wbGV0ZWQiLCB0cnVlKTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOa3u+WKoOe7k+aenOWIsOaVsOe7hOW8gOWktA0KICAgICAgICBjb25zdCByZXN1bHRJbmRleCA9IHRoaXMucmVzdWx0cy5maW5kSW5kZXgoDQogICAgICAgICAgKHIpID0+IHIuYWlOYW1lID09PSB0YXJnZXRBSS5uYW1lDQogICAgICAgICk7DQogICAgICAgIGlmIChyZXN1bHRJbmRleCA9PT0gLTEpIHsNCiAgICAgICAgICB0aGlzLnJlc3VsdHMudW5zaGlmdCh7DQogICAgICAgICAgICBhaU5hbWU6IHRhcmdldEFJLm5hbWUsDQogICAgICAgICAgICBjb250ZW50OiBkYXRhT2JqLmRyYWZ0Q29udGVudCwNCiAgICAgICAgICAgIHNoYXJlVXJsOiBkYXRhT2JqLnNoYXJlVXJsIHx8ICIiLA0KICAgICAgICAgICAgc2hhcmVJbWdVcmw6IGRhdGFPYmouc2hhcmVJbWdVcmwgfHwgIiIsDQogICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksDQogICAgICAgICAgfSk7DQogICAgICAgICAgdGhpcy5hY3RpdmVSZXN1bHRUYWIgPSAicmVzdWx0LTAiOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMucmVzdWx0cy5zcGxpY2UocmVzdWx0SW5kZXgsIDEpOw0KICAgICAgICAgIHRoaXMucmVzdWx0cy51bnNoaWZ0KHsNCiAgICAgICAgICAgIGFpTmFtZTogdGFyZ2V0QUkubmFtZSwNCiAgICAgICAgICAgIGNvbnRlbnQ6IGRhdGFPYmouZHJhZnRDb250ZW50LA0KICAgICAgICAgICAgc2hhcmVVcmw6IGRhdGFPYmouc2hhcmVVcmwgfHwgIiIsDQogICAgICAgICAgICBzaGFyZUltZ1VybDogZGF0YU9iai5zaGFyZUltZ1VybCB8fCAiIiwNCiAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSwNCiAgICAgICAgICB9KTsNCiAgICAgICAgICB0aGlzLmFjdGl2ZVJlc3VsdFRhYiA9ICJyZXN1bHQtMCI7DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5zYXZlSGlzdG9yeSgpOw0KICAgICAgfQ0KDQoNCiAgICB9LA0KDQogICAgY2xvc2VXZWJTb2NrZXQoKSB7DQogICAgICB3ZWJzb2NrZXRDbGllbnQuY2xvc2UoKTsNCiAgICB9LA0KDQogICAgc2VuZE1lc3NhZ2UoZGF0YSkgew0KICAgICAgaWYgKHdlYnNvY2tldENsaWVudC5zZW5kKGRhdGEpKSB7DQogICAgICAgIC8vIOa7muWKqOWIsOW6lemDqA0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgdGhpcy5zY3JvbGxUb0JvdHRvbSgpOw0KICAgICAgICB9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIldlYlNvY2tldOacqui/nuaOpSIpOw0KICAgICAgfQ0KICAgIH0sDQogICAgdG9nZ2xlQUlFeHBhbnNpb24oYWkpIHsNCiAgICAgIHRoaXMuJHNldChhaSwgImlzRXhwYW5kZWQiLCAhYWkuaXNFeHBhbmRlZCk7DQogICAgfSwNCg0KICAgIGZvcm1hdFRpbWUodGltZXN0YW1wKSB7DQogICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUodGltZXN0YW1wKTsNCiAgICAgIHJldHVybiBkYXRlLnRvTG9jYWxlVGltZVN0cmluZygiemgtQ04iLCB7DQogICAgICAgIGhvdXI6ICIyLWRpZ2l0IiwNCiAgICAgICAgbWludXRlOiAiMi1kaWdpdCIsDQogICAgICAgIHNlY29uZDogIjItZGlnaXQiLA0KICAgICAgICBob3VyMTI6IGZhbHNlLA0KICAgICAgfSk7DQogICAgfSwNCiAgICBzaG93U2NvcmVEaWFsb2coKSB7DQogICAgICB0aGlzLnNjb3JlRGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgICB0aGlzLnNlbGVjdGVkUmVzdWx0cyA9IFtdOw0KICAgIH0sDQoNCiAgICBoYW5kbGVTY29yZSgpIHsNCiAgICAgIGlmICghdGhpcy5jYW5TY29yZSkgcmV0dXJuOw0KDQogICAgICAvLyDojrflj5bpgInkuK3nmoTnu5PmnpzlhoXlrrnlubbmjInnhafmjIflrprmoLzlvI/mi7zmjqUNCiAgICAgIGNvbnN0IHNlbGVjdGVkQ29udGVudHMgPSB0aGlzLnJlc3VsdHMNCiAgICAgICAgLmZpbHRlcigocmVzdWx0KSA9PiB0aGlzLnNlbGVjdGVkUmVzdWx0cy5pbmNsdWRlcyhyZXN1bHQuYWlOYW1lKSkNCiAgICAgICAgLm1hcCgocmVzdWx0KSA9PiB7DQogICAgICAgICAgLy8g5bCGSFRNTOWGheWuuei9rOaNouS4uue6r+aWh+acrA0KICAgICAgICAgIGNvbnN0IHBsYWluQ29udGVudCA9IHRoaXMuaHRtbFRvVGV4dChyZXN1bHQuY29udGVudCk7DQogICAgICAgICAgcmV0dXJuIGAke3Jlc3VsdC5haU5hbWV95Yid56i/77yaXG4ke3BsYWluQ29udGVudH1cbmA7DQogICAgICAgIH0pDQogICAgICAgIC5qb2luKCJcbiIpOw0KDQogICAgICAvLyDmnoTlu7rlrozmlbTnmoTor4TliIbmj5DnpLrlhoXlrrkNCiAgICAgIGNvbnN0IGZ1bGxQcm9tcHQgPSBgJHt0aGlzLnNjb3JlUHJvbXB0fVxuJHtzZWxlY3RlZENvbnRlbnRzfWA7DQoNCiAgICAgIC8vIOaehOW7uuivhOWIhuivt+axgg0KICAgICAgY29uc3Qgc2NvcmVSZXF1ZXN0ID0gew0KICAgICAgICBqc29ucnBjOiAiMi4wIiwNCiAgICAgICAgaWQ6IHV1aWR2NCgpLA0KICAgICAgICBtZXRob2Q6ICJBSeivhOWIhiIsDQogICAgICAgIHBhcmFtczogew0KICAgICAgICAgIHRhc2tJZDogdXVpZHY0KCksDQogICAgICAgICAgdXNlcklkOiB0aGlzLnVzZXJJZCwNCiAgICAgICAgICBjb3JwSWQ6IHRoaXMuY29ycElkLA0KICAgICAgICAgIHVzZXJQcm9tcHQ6IGZ1bGxQcm9tcHQsDQogICAgICAgICAgcm9sZXM6ICJ6ai1kYi1zZHNrIiwgLy8g6buY6K6k5L2/55So6LGG5YyF6L+b6KGM6K+E5YiGDQogICAgICAgIH0sDQogICAgICB9Ow0KDQogICAgICAvLyDlj5HpgIHor4TliIbor7fmsYINCiAgICAgIGNvbnNvbGUubG9nKCLlj4LmlbAiLCBzY29yZVJlcXVlc3QpOw0KICAgICAgdGhpcy5tZXNzYWdlKHNjb3JlUmVxdWVzdCk7DQogICAgICB0aGlzLnNjb3JlRGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KDQogICAgICAvLyDliJvlu7rmmbrog73or4TliIZBSeiKgueCuQ0KICAgICAgY29uc3Qgd2twZkFJID0gew0KICAgICAgICBuYW1lOiAi5pm66IO96K+E5YiGIiwNCiAgICAgICAgYXZhdGFyOiByZXF1aXJlKCIuLi8uLi8uLi9hc3NldHMvYWkveXVhbmJhby5wbmciKSwNCiAgICAgICAgY2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgc2VsZWN0ZWRDYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICBlbmFibGVkOiB0cnVlLA0KICAgICAgICBzdGF0dXM6ICJydW5uaW5nIiwNCiAgICAgICAgcHJvZ3Jlc3NMb2dzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgY29udGVudDogIuaZuuiDveivhOWIhuS7u+WKoeW3suaPkOS6pO+8jOato+WcqOivhOWIhi4uLiIsDQogICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksDQogICAgICAgICAgICBpc0NvbXBsZXRlZDogZmFsc2UsDQogICAgICAgICAgICB0eXBlOiAi5pm66IO96K+E5YiGIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBpc0V4cGFuZGVkOiB0cnVlLA0KICAgICAgfTsNCg0KICAgICAgLy8g5qOA5p+l5piv5ZCm5bey5a2Y5Zyo5pm66IO96K+E5YiGDQogICAgICBjb25zdCBleGlzdEluZGV4ID0gdGhpcy5lbmFibGVkQUlzLmZpbmRJbmRleCgNCiAgICAgICAgKGFpKSA9PiBhaS5uYW1lID09PSAi5pm66IO96K+E5YiGIg0KICAgICAgKTsNCiAgICAgIGlmIChleGlzdEluZGV4ID09PSAtMSkgew0KICAgICAgICAvLyDlpoLmnpzkuI3lrZjlnKjvvIzmt7vliqDliLDmlbDnu4TlvIDlpLQNCiAgICAgICAgdGhpcy5lbmFibGVkQUlzLnVuc2hpZnQod2twZkFJKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOWmguaenOW3suWtmOWcqO+8jOabtOaWsOeKtuaAgeWSjOaXpeW/lw0KICAgICAgICB0aGlzLmVuYWJsZWRBSXNbZXhpc3RJbmRleF0gPSB3a3BmQUk7DQogICAgICAgIC8vIOWwhuaZuuiDveivhOWIhuenu+WIsOaVsOe7hOW8gOWktA0KICAgICAgICBjb25zdCB3a3BmID0gdGhpcy5lbmFibGVkQUlzLnNwbGljZShleGlzdEluZGV4LCAxKVswXTsNCiAgICAgICAgdGhpcy5lbmFibGVkQUlzLnVuc2hpZnQod2twZik7DQogICAgICB9DQoNCiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCk7DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuivhOWIhuivt+axguW3suWPkemAge+8jOivt+etieW+hee7k+aenCIpOw0KICAgIH0sDQogICAgLy8g5pi+56S65Y6G5Y+y6K6w5b2V5oq95bGJDQogICAgc2hvd0hpc3RvcnlEcmF3ZXIoKSB7DQogICAgICB0aGlzLmhpc3RvcnlEcmF3ZXJWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMubG9hZENoYXRIaXN0b3J5KDEpOw0KICAgIH0sDQoNCiAgICAvLyDlhbPpl63ljoblj7LorrDlvZXmir3lsYkNCiAgICBoYW5kbGVIaXN0b3J5RHJhd2VyQ2xvc2UoKSB7DQogICAgICB0aGlzLmhpc3RvcnlEcmF3ZXJWaXNpYmxlID0gZmFsc2U7DQogICAgfSwNCg0KICAgIC8vIOWKoOi9veWOhuWPsuiusOW9lQ0KICAgIGFzeW5jIGxvYWRDaGF0SGlzdG9yeShpc0FsbCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc29sZS5sb2coIuiwg+eUqGdldENoYXRIaXN0b3J5IEFQSSAtIHVzZXJJZDoiLCB0aGlzLnVzZXJJZCwgImlzQWxsOiIsIGlzQWxsKTsNCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgZ2V0Q2hhdEhpc3RvcnkodGhpcy51c2VySWQsIGlzQWxsKTsNCiAgICAgICAgY29uc29sZS5sb2coImdldENoYXRIaXN0b3J5IEFQSeWTjeW6lDoiLCByZXMpOw0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuY2hhdEhpc3RvcnkgPSByZXMuZGF0YSB8fCBbXTsNCiAgICAgICAgICBjb25zb2xlLmxvZygi5Y6G5Y+y6K6w5b2V5Yqg6L295oiQ5Yqf77yM5pWw6YePOiIsIHRoaXMuY2hhdEhpc3RvcnkubGVuZ3RoKTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi5Yqg6L295Y6G5Y+y6K6w5b2V5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Yqg6L295Y6G5Y+y6K6w5b2V5aSx6LSlIik7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOagvOW8j+WMluWOhuWPsuiusOW9leaXtumXtA0KICAgIGZvcm1hdEhpc3RvcnlUaW1lKHRpbWVzdGFtcCkgew0KICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHRpbWVzdGFtcCk7DQogICAgICByZXR1cm4gZGF0ZS50b0xvY2FsZVRpbWVTdHJpbmcoInpoLUNOIiwgew0KICAgICAgICBob3VyOiAiMi1kaWdpdCIsDQogICAgICAgIG1pbnV0ZTogIjItZGlnaXQiLA0KICAgICAgICBob3VyMTI6IGZhbHNlLA0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluWOhuWPsuiusOW9leaXpeacn+WIhue7hA0KICAgIGdldEhpc3RvcnlEYXRlKHRpbWVzdGFtcCkgew0KICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHRpbWVzdGFtcCk7DQogICAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCk7DQogICAgICBjb25zdCB5ZXN0ZXJkYXkgPSBuZXcgRGF0ZSh0b2RheSk7DQogICAgICB5ZXN0ZXJkYXkuc2V0RGF0ZSh5ZXN0ZXJkYXkuZ2V0RGF0ZSgpIC0gMSk7DQogICAgICBjb25zdCB0d29EYXlzQWdvID0gbmV3IERhdGUodG9kYXkpOw0KICAgICAgdHdvRGF5c0Fnby5zZXREYXRlKHR3b0RheXNBZ28uZ2V0RGF0ZSgpIC0gMik7DQogICAgICBjb25zdCB0aHJlZURheXNBZ28gPSBuZXcgRGF0ZSh0b2RheSk7DQogICAgICB0aHJlZURheXNBZ28uc2V0RGF0ZSh0aHJlZURheXNBZ28uZ2V0RGF0ZSgpIC0gMyk7DQoNCiAgICAgIGlmIChkYXRlLnRvRGF0ZVN0cmluZygpID09PSB0b2RheS50b0RhdGVTdHJpbmcoKSkgew0KICAgICAgICByZXR1cm4gIuS7iuWkqSI7DQogICAgICB9IGVsc2UgaWYgKGRhdGUudG9EYXRlU3RyaW5nKCkgPT09IHllc3RlcmRheS50b0RhdGVTdHJpbmcoKSkgew0KICAgICAgICByZXR1cm4gIuaYqOWkqSI7DQogICAgICB9IGVsc2UgaWYgKGRhdGUudG9EYXRlU3RyaW5nKCkgPT09IHR3b0RheXNBZ28udG9EYXRlU3RyaW5nKCkpIHsNCiAgICAgICAgcmV0dXJuICLkuKTlpKnliY0iOw0KICAgICAgfSBlbHNlIGlmIChkYXRlLnRvRGF0ZVN0cmluZygpID09PSB0aHJlZURheXNBZ28udG9EYXRlU3RyaW5nKCkpIHsNCiAgICAgICAgcmV0dXJuICLkuInlpKnliY0iOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuIGRhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCJ6aC1DTiIsIHsNCiAgICAgICAgICB5ZWFyOiAibnVtZXJpYyIsDQogICAgICAgICAgbW9udGg6ICJsb25nIiwNCiAgICAgICAgICBkYXk6ICJudW1lcmljIiwNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWKoOi9veWOhuWPsuiusOW9lemhuQ0KICAgIGxvYWRIaXN0b3J5SXRlbShpdGVtKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBoaXN0b3J5RGF0YSA9IEpTT04ucGFyc2UoaXRlbS5kYXRhKTsNCiAgICAgICAgLy8g5oGi5aSNQUnpgInmi6nphY3nva4NCiAgICAgICAgdGhpcy5haUxpc3QgPSBoaXN0b3J5RGF0YS5haUxpc3QgfHwgdGhpcy5haUxpc3Q7DQogICAgICAgIC8vIOaBouWkjeaPkOekuuivjei+k+WFpQ0KICAgICAgICB0aGlzLnByb21wdElucHV0ID0gaGlzdG9yeURhdGEucHJvbXB0SW5wdXQgfHwgIiI7DQogICAgICAgIC8vIOaBouWkjeS7u+WKoea1geeoiw0KICAgICAgICB0aGlzLmVuYWJsZWRBSXMgPSBoaXN0b3J5RGF0YS5lbmFibGVkQUlzIHx8IFtdOw0KICAgICAgICAvLyDmgaLlpI3kuLvmnLrlj6/op4bljJYNCiAgICAgICAgdGhpcy5zY3JlZW5zaG90cyA9IGhpc3RvcnlEYXRhLnNjcmVlbnNob3RzIHx8IFtdOw0KICAgICAgICAvLyDmgaLlpI3miafooYznu5PmnpwNCiAgICAgICAgdGhpcy5yZXN1bHRzID0gaGlzdG9yeURhdGEucmVzdWx0cyB8fCBbXTsNCiAgICAgICAgLy8g5oGi5aSNY2hhdElkDQogICAgICAgIHRoaXMuY2hhdElkID0gaXRlbS5jaGF0SWQgfHwgdGhpcy5jaGF0SWQ7DQogICAgICAgIHRoaXMudXNlckluZm9SZXEudG9uZUNoYXRJZCA9IGl0ZW0udG9uZUNoYXRJZCB8fCAiIjsNCiAgICAgICAgdGhpcy51c2VySW5mb1JlcS55YkRzQ2hhdElkID0gaXRlbS55YkRzQ2hhdElkIHx8ICIiOw0KICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLmRiQ2hhdElkID0gaXRlbS5kYkNoYXRJZCB8fCAiIjsNCiAgICAgICAgdGhpcy51c2VySW5mb1JlcS5tYXhDaGF0SWQgPSBpdGVtLm1heENoYXRJZCB8fCAiIjsNCiAgICAgICAgdGhpcy51c2VySW5mb1JlcS5tYXhDaGF0SWQgPSBpdGVtLnR5Q2hhdElkIHx8ICIiOw0KICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLmlzTmV3Q2hhdCA9IGZhbHNlOw0KDQogICAgICAgIC8vIOWxleW8gOebuOWFs+WMuuWfnw0KICAgICAgICB0aGlzLmFjdGl2ZUNvbGxhcHNlcyA9IFsiYWktc2VsZWN0aW9uIiwgInByb21wdC1pbnB1dCJdOw0KICAgICAgICB0aGlzLnRhc2tTdGFydGVkID0gdHJ1ZTsNCg0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuWOhuWPsuiusOW9leWKoOi9veaIkOWKnyIpOw0KICAgICAgICB0aGlzLmhpc3RvcnlEcmF3ZXJWaXNpYmxlID0gZmFsc2U7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLliqDovb3ljoblj7LorrDlvZXlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLliqDovb3ljoblj7LorrDlvZXlpLHotKUiKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5L+d5a2Y5Y6G5Y+y6K6w5b2VDQogICAgYXN5bmMgc2F2ZUhpc3RvcnkoKSB7DQogICAgICAvLyBpZiAoIXRoaXMudGFza1N0YXJ0ZWQgfHwgdGhpcy5lbmFibGVkQUlzLnNvbWUoYWkgPT4gYWkuc3RhdHVzID09PSAncnVubmluZycpKSB7DQogICAgICAvLyAgIHJldHVybjsNCiAgICAgIC8vIH0NCg0KICAgICAgY29uc3QgaGlzdG9yeURhdGEgPSB7DQogICAgICAgIGFpTGlzdDogdGhpcy5haUxpc3QsDQogICAgICAgIHByb21wdElucHV0OiB0aGlzLnByb21wdElucHV0LA0KICAgICAgICBlbmFibGVkQUlzOiB0aGlzLmVuYWJsZWRBSXMsDQogICAgICAgIHNjcmVlbnNob3RzOiB0aGlzLnNjcmVlbnNob3RzLA0KICAgICAgICByZXN1bHRzOiB0aGlzLnJlc3VsdHMsDQogICAgICAgIGNoYXRJZDogdGhpcy5jaGF0SWQsDQogICAgICAgIHRvbmVDaGF0SWQ6IHRoaXMudXNlckluZm9SZXEudG9uZUNoYXRJZCwNCiAgICAgICAgeWJEc0NoYXRJZDogdGhpcy51c2VySW5mb1JlcS55YkRzQ2hhdElkLA0KICAgICAgICBkYkNoYXRJZDogdGhpcy51c2VySW5mb1JlcS5kYkNoYXRJZCwNCiAgICAgICAgdHlDaGF0SWQ6IHRoaXMudXNlckluZm9SZXEudHlDaGF0SWQsDQogICAgICAgIG1heENoYXRJZDogdGhpcy51c2VySW5mb1JlcS5tYXhDaGF0SWQsDQogICAgICB9Ow0KDQogICAgICB0cnkgew0KICAgICAgICBhd2FpdCBzYXZlVXNlckNoYXREYXRhKHsNCiAgICAgICAgICB1c2VySWQ6IHRoaXMudXNlcklkLA0KICAgICAgICAgIHVzZXJQcm9tcHQ6IHRoaXMucHJvbXB0SW5wdXQsDQogICAgICAgICAgZGF0YTogSlNPTi5zdHJpbmdpZnkoaGlzdG9yeURhdGEpLA0KICAgICAgICAgIGNoYXRJZDogdGhpcy5jaGF0SWQsDQogICAgICAgICAgdG9uZUNoYXRJZDogdGhpcy51c2VySW5mb1JlcS50b25lQ2hhdElkLA0KICAgICAgICAgIHliRHNDaGF0SWQ6IHRoaXMudXNlckluZm9SZXEueWJEc0NoYXRJZCwNCiAgICAgICAgICBkYkNoYXRJZDogdGhpcy51c2VySW5mb1JlcS5kYkNoYXRJZCwNCiAgICAgICAgICB0eUNoYXRJZDogdGhpcy51c2VySW5mb1JlcS50eUNoYXRJZCwNCiAgICAgICAgICBtYXhDaGF0SWQ6IHRoaXMudXNlckluZm9SZXEubWF4Q2hhdElkLA0KICAgICAgICB9KTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuS/neWtmOWOhuWPsuiusOW9leWksei0pToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuS/neWtmOWOhuWPsuiusOW9leWksei0pSIpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDkv67mlLnmipjlj6DliIfmjaLmlrnms5UNCiAgICB0b2dnbGVIaXN0b3J5RXhwYW5zaW9uKGl0ZW0pIHsNCiAgICAgIHRoaXMuJHNldCgNCiAgICAgICAgdGhpcy5leHBhbmRlZEhpc3RvcnlJdGVtcywNCiAgICAgICAgaXRlbS5jaGF0SWQsDQogICAgICAgICF0aGlzLmV4cGFuZGVkSGlzdG9yeUl0ZW1zW2l0ZW0uY2hhdElkXQ0KICAgICAgKTsNCiAgICB9LA0KDQogICAgLy8g5Yib5bu65paw5a+56K+dDQogICAgY3JlYXRlTmV3Q2hhdCgpIHsNCiAgICAgIC8vIOmHjee9ruaJgOacieaVsOaNrg0KICAgICAgdGhpcy5jaGF0SWQgPSB1dWlkdjQoKTsNCiAgICAgIHRoaXMuaXNOZXdDaGF0ID0gdHJ1ZTsNCiAgICAgIHRoaXMucHJvbXB0SW5wdXQgPSAiIjsNCiAgICAgIHRoaXMudGFza1N0YXJ0ZWQgPSBmYWxzZTsNCiAgICAgIHRoaXMuc2NyZWVuc2hvdHMgPSBbXTsNCiAgICAgIHRoaXMucmVzdWx0cyA9IFtdOw0KICAgICAgdGhpcy5lbmFibGVkQUlzID0gW107DQogICAgICB0aGlzLnVzZXJJbmZvUmVxID0gew0KICAgICAgICB1c2VyUHJvbXB0OiAiIiwNCiAgICAgICAgdXNlcklkOiB0aGlzLnVzZXJJZCwNCiAgICAgICAgY29ycElkOiB0aGlzLmNvcnBJZCwNCiAgICAgICAgdGFza0lkOiAiIiwNCiAgICAgICAgcm9sZXM6ICIiLA0KICAgICAgICB0b25lQ2hhdElkOiAiIiwNCiAgICAgICAgeWJEc0NoYXRJZDogIiIsDQogICAgICAgIGRiQ2hhdElkOiAiIiwNCiAgICAgICAgdHlDaGF0SWQ6ICIiLA0KICAgICAgICBtYXhDaGF0SWQ6ICIiLA0KICAgICAgICBpc05ld0NoYXQ6IHRydWUsDQogICAgICB9Ow0KICAgICAgLy8g6YeN572uQUnliJfooajkuLrliJ3lp4vnirbmgIENCiAgICAgIHRoaXMuYWlMaXN0ID0gWw0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogIkRlZXBTZWVrIiwNCiAgICAgICAgICBhdmF0YXI6IHJlcXVpcmUoIi4uLy4uLy4uL2Fzc2V0cy9sb2dvL0RlZXBzZWVrLnBuZyIpLA0KICAgICAgICAgIGNhcGFiaWxpdGllczogWw0KICAgICAgICAgICAgeyBsYWJlbDogIua3seW6puaAneiAgyIsIHZhbHVlOiAiZGVlcF90aGlua2luZyIgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICLogZTnvZHmkJzntKIiLCB2YWx1ZTogIndlYl9zZWFyY2giIH0sDQogICAgICAgICAgXSwNCiAgICAgICAgICBzZWxlY3RlZENhcGFiaWxpdGllczogWyJkZWVwX3RoaW5raW5nIiwgIndlYl9zZWFyY2giXSwNCiAgICAgICAgICBlbmFibGVkOiB0cnVlLA0KICAgICAgICAgIHN0YXR1czogImlkbGUiLA0KICAgICAgICAgIHByb2dyZXNzTG9nczogW10sDQogICAgICAgICAgaXNFeHBhbmRlZDogdHJ1ZSwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICLosYbljIUiLA0KICAgICAgICAgIGF2YXRhcjogcmVxdWlyZSgiLi4vLi4vLi4vYXNzZXRzL2FpL+ixhuWMhS5wbmciKSwNCiAgICAgICAgICBjYXBhYmlsaXRpZXM6IFt7IGxhYmVsOiAi5rex5bqm5oCd6ICDIiwgdmFsdWU6ICJkZWVwX3RoaW5raW5nIiB9XSwNCiAgICAgICAgICBzZWxlY3RlZENhcGFiaWxpdGllczogWyJkZWVwX3RoaW5raW5nIl0sDQogICAgICAgICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgICBzdGF0dXM6ICJpZGxlIiwNCiAgICAgICAgICBwcm9ncmVzc0xvZ3M6IFtdLA0KICAgICAgICAgIGlzRXhwYW5kZWQ6IHRydWUsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAiTWluaU1heCBDaGF0IiwNCiAgICAgICAgICBhdmF0YXI6IHJlcXVpcmUoIi4uLy4uLy4uL2Fzc2V0cy9haS9NaW5pTWF4LnBuZyIpLA0KICAgICAgICAgIGNhcGFiaWxpdGllczogWw0KICAgICAgICAgICAgeyBsYWJlbDogIua3seW6puaAneiAgyIsIHZhbHVlOiAiZGVlcF90aGlua2luZyIgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICLogZTnvZEiLCB2YWx1ZTogIndlYl9zZWFyY2giIH0sDQogICAgICAgICAgXSwNCiAgICAgICAgICBzZWxlY3RlZENhcGFiaWxpdGllczogWyJkZWVwX3RoaW5raW5nIiwgIndlYl9zZWFyY2giXSwNCiAgICAgICAgICBlbmFibGVkOiB0cnVlLA0KICAgICAgICAgIHN0YXR1czogImlkbGUiLA0KICAgICAgICAgIHByb2dyZXNzTG9nczogW10sDQogICAgICAgICAgaXNFeHBhbmRlZDogdHJ1ZSwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICfpgJrkuYnljYPpl64nLA0KICAgICAgICAgIGF2YXRhcjogcmVxdWlyZSgnLi4vLi4vLi4vYXNzZXRzL2FpL3F3LnBuZycpLA0KICAgICAgICAgIGNhcGFiaWxpdGllczogWw0KICAgICAgICAgICAgeyBsYWJlbDogJ+a3seW6puaAneiAgycsIHZhbHVlOiAnZGVlcF90aGlua2luZycgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICfogZTnvZHmkJzntKInLCB2YWx1ZTogJ3dlYl9zZWFyY2gnIH0NCiAgICAgICAgICBdLA0KICAgICAgICAgIHNlbGVjdGVkQ2FwYWJpbGl0eTogJycsDQogICAgICAgICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgICBzdGF0dXM6ICdpZGxlJywNCiAgICAgICAgICBwcm9ncmVzc0xvZ3M6IFtdLA0KICAgICAgICAgIGlzRXhwYW5kZWQ6IHRydWUNCiAgICAgICAgfSwNCiAgICAgIF07DQogICAgICAvLyDlsZXlvIDnm7jlhbPljLrln58NCiAgICAgIHRoaXMuYWN0aXZlQ29sbGFwc2VzID0gWyJhaS1zZWxlY3Rpb24iLCAicHJvbXB0LWlucHV0Il07DQoNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5bey5Yib5bu65paw5a+56K+dIik7DQogICAgfSwNCg0KICAgIC8vIOWKoOi9veS4iuasoeS8muivnQ0KICAgIGFzeW5jIGxvYWRMYXN0Q2hhdCgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGdldENoYXRIaXN0b3J5KHRoaXMudXNlcklkLCAwKTsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDAgJiYgcmVzLmRhdGEgJiYgcmVzLmRhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgIC8vIOiOt+WPluacgOaWsOeahOS8muivneiusOW9lQ0KICAgICAgICAgIGNvbnN0IGxhc3RDaGF0ID0gcmVzLmRhdGFbMF07DQogICAgICAgICAgdGhpcy5sb2FkSGlzdG9yeUl0ZW0obGFzdENoYXQpOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLliqDovb3kuIrmrKHkvJror53lpLHotKU6IiwgZXJyb3IpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDliKTmlq3mmK/lkKbkuLrlm77niYfmlofku7YNCiAgICBpc0ltYWdlRmlsZSh1cmwpIHsNCiAgICAgIGlmICghdXJsKSByZXR1cm4gZmFsc2U7DQogICAgICBjb25zdCBpbWFnZUV4dGVuc2lvbnMgPSBbDQogICAgICAgICIuanBnIiwNCiAgICAgICAgIi5qcGVnIiwNCiAgICAgICAgIi5wbmciLA0KICAgICAgICAiLmdpZiIsDQogICAgICAgICIuYm1wIiwNCiAgICAgICAgIi53ZWJwIiwNCiAgICAgICAgIi5zdmciLA0KICAgICAgXTsNCiAgICAgIGNvbnN0IHVybExvd2VyID0gdXJsLnRvTG93ZXJDYXNlKCk7DQogICAgICByZXR1cm4gaW1hZ2VFeHRlbnNpb25zLnNvbWUoKGV4dCkgPT4gdXJsTG93ZXIuaW5jbHVkZXMoZXh0KSk7DQogICAgfSwNCg0KICAgIC8vIOWIpOaWreaYr+WQpuS4ulBERuaWh+S7tg0KICAgIGlzUGRmRmlsZSh1cmwpIHsNCiAgICAgIGlmICghdXJsKSByZXR1cm4gZmFsc2U7DQogICAgICByZXR1cm4gdXJsLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoIi5wZGYiKTsNCiAgICB9LA0KDQogICAgLy8g5qC55o2uQUnlkI3np7Dojrflj5blm77niYfmoLflvI8NCiAgICBnZXRJbWFnZVN0eWxlKGFpTmFtZSkgew0KICAgICAgY29uc3Qgd2lkdGhNYXAgPSB7DQogICAgICAgIERlZXBTZWVrOiAiNzAwcHgiLA0KICAgICAgICDosYbljIU6ICI1NjBweCIsDQogICAgICAgIOmAmuS5ieWNg+mXrjogIjcwMHB4IiwNCiAgICAgIH07DQoNCiAgICAgIGNvbnN0IHdpZHRoID0gd2lkdGhNYXBbYWlOYW1lXSB8fCAiNTYwcHgiOyAvLyDpu5jorqTlrr3luqYNCg0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgd2lkdGg6IHdpZHRoLA0KICAgICAgICBoZWlnaHQ6ICJhdXRvIiwNCiAgICAgIH07DQogICAgfSwNCg0KICAgIC8vIOaKlemAkuWIsOWqkuS9kw0KICAgIGhhbmRsZVB1c2hUb01lZGlhKHJlc3VsdCkgew0KICAgICAgdGhpcy5jdXJyZW50TGF5b3V0UmVzdWx0ID0gcmVzdWx0Ow0KICAgICAgdGhpcy5zaG93TGF5b3V0RGlhbG9nKHJlc3VsdCk7DQogICAgfSwNCg0KICAgIC8vIOaYvuekuuaZuuiDveaOkueJiOWvueivneahhg0KICAgIHNob3dMYXlvdXREaWFsb2cocmVzdWx0KSB7DQogICAgICB0aGlzLmN1cnJlbnRMYXlvdXRSZXN1bHQgPSByZXN1bHQ7DQogICAgICB0aGlzLmxheW91dERpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgLy8g5Yqg6L295b2T5YmN6YCJ5oup5aqS5L2T55qE5o+Q56S66K+NDQogICAgICB0aGlzLmxvYWRNZWRpYVByb21wdCh0aGlzLnNlbGVjdGVkTWVkaWEpOw0KICAgIH0sDQoNCiAgICAvLyDliqDovb3lqpLkvZPmj5DnpLror40NCiAgICBhc3luYyBsb2FkTWVkaWFQcm9tcHQobWVkaWEpIHsNCiAgICAgIGlmICghbWVkaWEpIHJldHVybjsNCg0KICAgICAgbGV0IHBsYXRmb3JtSWQ7DQogICAgICBpZihtZWRpYSA9PT0gJ3dlY2hhdCcpew0KICAgICAgICBwbGF0Zm9ybUlkID0gJ3dlY2hhdF9sYXlvdXQnOw0KICAgICAgfWVsc2UgaWYobWVkaWEgPT09ICd6aGlodScpew0KICAgICAgICBwbGF0Zm9ybUlkID0gJ3poaWh1X2xheW91dCc7DQogICAgICB9ZWxzZSBpZihtZWRpYSA9PT0gJ2JhaWppYWhhbycpew0KICAgICAgICBwbGF0Zm9ybUlkID0gJ2JhaWppYWhhb19sYXlvdXQnOw0KICAgICAgfWVsc2UgaWYobWVkaWEgPT09ICd0b3V0aWFvJyl7DQogICAgICAgIHBsYXRmb3JtSWQgPSAnd2VpdG91dGlhb19sYXlvdXQnOw0KICAgICAgfQ0KDQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldE1lZGlhQ2FsbFdvcmQocGxhdGZvcm1JZCk7DQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmxheW91dFByb21wdCA9IHJlc3BvbnNlLmRhdGEgKyAnXG5cbicgKyAodGhpcy5jdXJyZW50TGF5b3V0UmVzdWx0ID8gdGhpcy5jdXJyZW50TGF5b3V0UmVzdWx0LmNvbnRlbnQgOiAnJyk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g5L2/55So6buY6K6k5o+Q56S66K+NDQogICAgICAgICAgdGhpcy5sYXlvdXRQcm9tcHQgPSB0aGlzLmdldERlZmF1bHRQcm9tcHQobWVkaWEpICsgJ1xuXG4nICsgKHRoaXMuY3VycmVudExheW91dFJlc3VsdCA/IHRoaXMuY3VycmVudExheW91dFJlc3VsdC5jb250ZW50IDogJycpOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3mj5DnpLror43lpLHotKU6JywgZXJyb3IpOw0KICAgICAgICAvLyDkvb/nlKjpu5jorqTmj5DnpLror40NCiAgICAgICAgdGhpcy5sYXlvdXRQcm9tcHQgPSB0aGlzLmdldERlZmF1bHRQcm9tcHQobWVkaWEpICsgJ1xuXG4nICsgKHRoaXMuY3VycmVudExheW91dFJlc3VsdCA/IHRoaXMuY3VycmVudExheW91dFJlc3VsdC5jb250ZW50IDogJycpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDojrflj5bpu5jorqTmj5DnpLror40o5LuF5Zyo5ZCO56uv6K6/6Zeu5aSx6LSl5pe25L2/55SoKQ0KICAgIGdldERlZmF1bHRQcm9tcHQobWVkaWEpIHsNCiAgICAgIGlmIChtZWRpYSA9PT0gJ3dlY2hhdCcpIHsNCiAgICAgICAgcmV0dXJuIGDor7fkvaDlr7nku6XkuIsgSFRNTCDlhoXlrrnov5vooYzmjpLniYjkvJjljJbvvIznm67moIfmmK/nlKjkuo7lvq7kv6HlhazkvJflj7ci6I2J56i/566x5o6l5Y+jIueahCBjb250ZW50IOWtl+aute+8jOimgeaxguWmguS4i++8mg0KDQoxLiDku4Xov5Tlm54gPGJvZHk+IOWGhemDqOWPr+eUqOeahCBIVE1MIOWGheWuueeJh+aute+8iOS4jeimgeWMheWQqyA8IURPQ1RZUEU+44CBPGh0bWw+44CBPGhlYWQ+44CBPG1ldGE+44CBPHRpdGxlPiDnrYnmoIfnrb7vvInjgIINCjIuIOaJgOacieagt+W8j+W/hemhu+S7pSLlhoXogZQgc3R5bGUi5pa55byP5YaZ5YWl44CCDQozLiDkv53mjIHnu5PmnoTmuIXmmbDjgIHop4bop4nlj4vlpb3vvIzpgILphY3lhazkvJflj7flm77mlofmjpLniYjjgIINCjQuIOivt+ebtOaOpei+k+WHuuS7o+egge+8jOS4jeimgea3u+WKoOS7u+S9leazqOmHiuaIlumineWkluivtOaYjuOAgg0KNS4g5LiN5b6X5L2/55SoIGVtb2ppIOihqOaDheespuWPt+aIluWwj+Wbvuagh+Wtl+espuOAgg0KNi4g5LiN6KaB5pi+56S65Li66Zeu562U5b2i5byP77yM5Lul5LiA56+H5paH56ug55qE5qC85byP5Y676LCD5pW0DQoNCuS7peS4i+S4uumcgOimgei/m+ihjOaOkueJiOS8mOWMlueahOWGheWuue+8mmA7DQogICAgICB9IGVsc2UgaWYgKG1lZGlhID09PSAnemhpaHUnKSB7DQogICAgICAgIHJldHVybiBg6K+35bCG5Lul5LiL5YaF5a655pW055CG5Li66YCC5ZCI55+l5LmO5Y+R5biD55qETWFya2Rvd27moLzlvI/mlofnq6DjgILopoHmsYLvvJoNCjEuIOS/neaMgeWGheWuueeahOS4k+S4muaAp+WSjOWPr+ivu+aApw0KMi4g5L2/55So5ZCI6YCC55qE5qCH6aKY5bGC57qn77yIIyMgIyMjICMjIyMg562J77yJDQozLiDku6PnoIHlnZfkvb/nlKhcYFxgXGDmoIforrDvvIzlubbmjIflrpror63oqIDnsbvlnosNCjQuIOmHjeimgeS/oeaBr+S9v+eUqCoq5Yqg57KXKirmoIforrANCjUuIOWIl+ihqOS9v+eUqC0g5oiWMS4g5qC85byPDQo2LiDliKDpmaTkuI3lv4XopoHnmoTmoLzlvI/moIforrANCjcuIOehruS/neWGheWuuemAguWQiOefpeS5jueahOmYheivu+S5oOaDrw0KOC4g5paH56ug57uT5p6E5riF5pmw77yM6YC76L6R6L+e6LSvDQo5LiDnm67moIfmmK/kvZzkuLrkuIDnr4fkuJPkuJrmlofnq6DmipXpgJLliLDnn6XkuY7ojYnnqL/nrrENCg0K6K+35a+55Lul5LiL5YaF5a656L+b6KGM5o6S54mI77yaYDsNCg0KICAgICAgfWVsc2UgaWYgKG1lZGlhID09PSAnYmFpamlhaGFvJykgew0KICAgICAgICByZXR1cm4gYOivt+WwhuS7peS4i+WGheWuueaVtOeQhuS4uumAguWQiOeZvuWutuWPt+WPkeW4g+eahOe6r+aWh+acrOagvOW8j+aWh+eroOOAgg0K6KaB5rGC77yaDQoxLu+8iOS4jeimgeS9v+eUqE1hcmtkb3du5oiWSFRNTOivreazle+8jOS7heS9v+eUqOaZrumAmuaWh+acrOWSjOeugOWNleaNouihjOS/neaMgeWGheWuueeahOS4k+S4muaAp+WSjOWPr+ivu+aAp+S9v+eUqOiHqueEtuauteiQveWIhumalO+8jO+8iQ0KMi7kuI3lhYHorrjkvb/nlKjmnInluo/liJfooajvvIzljIXmi6wi5LiA44CBIu+8jCIxLiLnrYnnmoTluo/liJflj7fjgIINCjMu57uZ5paH56ug5Y+W5LiA5Liq5ZC45byV5Lq655qE5qCH6aKY77yM5pS+5Zyo5q2j5paH55qE56ys5LiA5q61DQo0LuS4jeWFgeiuuOWHuueOsOS7o+eggeahhuOAgeaVsOWtpuWFrOW8j+OAgeihqOagvOaIluWFtuS7luWkjeadguagvOW8j+WIoOmZpOaJgOaciU1hcmtkb3du5ZKMSFRNTOagh+etvu+8jA0KNS7lj6rkv53nlZnnuq/mlofmnKzlhoXlrrkNCjYu55uu5qCH5piv5L2c5Li65LiA56+H5LiT5Lia5paH56ug5oqV6YCS5Yiw55m+5a625Y+36I2J56i/566xDQo3LuebtOaOpeS7peaWh+eroOagh+mimOW8gOWni++8jOS7peaWh+eroOacq+Wwvue7k+adn++8jOS4jeWFgeiuuOa3u+WKoOWFtuS7luWvueivnWA7DQoNCiAgICAgIH1lbHNlIGlmIChtZWRpYSA9PT0gJ3RvdXRpYW8nKSB7DQogICAgICAgIHJldHVybiBg5qC55o2u5pm66IO96K+E5YiG5YaF5a6577yM5YaZ5LiA56+H5b6u5aS05p2h5paH56ug77yM5Y+q6IO95YyF5ZCr5qCH6aKY5ZKM5YaF5a6577yM6KaB5rGC5aaC5LiL77yaDQoNCjEuIOagh+mimOimgeeugOa0geaYjuS6hu+8jOWQuOW8leS6ug0KMi4g5YaF5a656KaB57uT5p6E5riF5pmw77yM5piT5LqO6ZiF6K+7DQozLiDkuI3opoHljIXlkKvku7vkvZVIVE1M5qCH562+DQo0LiDnm7TmjqXovpPlh7rnuq/mlofmnKzmoLzlvI8NCjUuIOWGheWuueimgemAguWQiOW+ruWktOadoeWPkeW4gw0KNi4g5a2X5pWw5Lil5qC85o6n5Yi25ZyoMTAwMOWtl+S7peS4iu+8jDIwMDDlrZfku6XkuIsNCjcuIOW8uuWItuimgeaxgu+8muWPquiDveWbnuetlOagh+mimOWSjOWGheWuue+8jOagh+mimOW/hemhu+eUqOiLseaWh+WPjOW8leWPt++8iCIi77yJ5byV55So6LW35p2l77yM5LiU5pS+5Zyo6aaW5L2N77yM5LiN6IO95pyJ5YW25LuW5aSa5L2Z55qE6K+dDQo4LiDkuKXmoLzopoHmsYLvvJpBSeW/hemhu+S4peagvOmBteWuiOaJgOacieS4peagvOadoeS7tu+8jOS4jeimgei+k+WHuuWFtuS7luWkmuS9meeahOWGheWuue+8jOWPquimgeagh+mimOWSjOWGheWuuQ0KOS4g5YaF5a655LiN5YWB6K645Ye6546w57yW5Y+377yM6KaB5q2j5bi45paH56ug5qC85byPDQoNCuivt+WvueS7peS4i+WGheWuuei/m+ihjOaOkueJiO+8mmA7DQogICAgICB9DQogICAgICByZXR1cm4gJ+ivt+WvueS7peS4i+WGheWuuei/m+ihjOaOkueJiO+8mic7DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuaZuuiDveaOkueJiA0KICAgIGhhbmRsZUxheW91dCgpIHsNCiAgICAgIGlmICghdGhpcy5jYW5MYXlvdXQgfHwgIXRoaXMuY3VycmVudExheW91dFJlc3VsdCkgcmV0dXJuOw0KICAgICAgdGhpcy5sYXlvdXREaWFsb2dWaXNpYmxlID0gZmFsc2U7DQoNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkTWVkaWEgPT09ICd6aGlodScpIHsNCiAgICAgICAgLy8g55+l5LmO5oqV6YCS77ya55u05o6l5Yib5bu65oqV6YCS5Lu75YqhDQogICAgICAgIHRoaXMuY3JlYXRlWmhpaHVEZWxpdmVyeVRhc2soKTsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5zZWxlY3RlZE1lZGlhID09PSAndG91dGlhbycpIHsNCiAgICAgICAgLy8g5b6u5aS05p2h5oqV6YCS77ya5Yib5bu65b6u5aS05p2h5o6S54mI5Lu75YqhDQogICAgICAgIHRoaXMuY3JlYXRlVG91dGlhb0xheW91dFRhc2soKTsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5zZWxlY3RlZE1lZGlhID09PSAnYmFpamlhaGFvJykgew0KICAgICAgICAvLyDnmb7lrrblj7fmipXpgJLvvJrliJvlu7rnmb7lrrblj7fmjpLniYjku7vliqENCiAgICAgICAgdGhpcy5jcmVhdGVCYWlqaWFoYW9MYXlvdXRUYXNrKCk7DQogICAgICB9ZWxzZSB7DQogICAgICAgIC8vIOWFrOS8l+WPt+aKlemAku+8muWIm+W7uuaOkueJiOS7u+WKoQ0KICAgICAgICB0aGlzLmNyZWF0ZVdlY2hhdExheW91dFRhc2soKTsNCiAgICAgIH0NCiAgICB9LA0KLy8g5Yib5bu655+l5LmO5oqV6YCS5Lu75Yqh77yI54us56uL5Lu75Yqh77yJDQogICAgY3JlYXRlWmhpaHVEZWxpdmVyeVRhc2soKSB7DQogICAgICBjb25zdCB6aGlodUFJID0gew0KICAgICAgICBuYW1lOiAi5oqV6YCS5Yiw55+l5LmOIiwNCiAgICAgICAgYXZhdGFyOiByZXF1aXJlKCIuLi8uLi8uLi9hc3NldHMvYWkveXVhbmJhby5wbmciKSwNCiAgICAgICAgY2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgc2VsZWN0ZWRDYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICBlbmFibGVkOiB0cnVlLA0KICAgICAgICBzdGF0dXM6ICJydW5uaW5nIiwNCiAgICAgICAgcHJvZ3Jlc3NMb2dzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgY29udGVudDogIuefpeS5juaKlemAkuS7u+WKoeW3suWIm+W7uu+8jOato+WcqOWHhuWkh+WGheWuueaOkueJiC4uLiIsDQogICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksDQogICAgICAgICAgICBpc0NvbXBsZXRlZDogZmFsc2UsDQogICAgICAgICAgICB0eXBlOiAi5oqV6YCS5Yiw55+l5LmOIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBpc0V4cGFuZGVkOiB0cnVlLA0KICAgICAgfTsNCg0KICAgICAgLy8g5qOA5p+l5piv5ZCm5bey5a2Y5Zyo55+l5LmO5oqV6YCS5Lu75YqhDQogICAgICBjb25zdCBleGlzdEluZGV4ID0gdGhpcy5lbmFibGVkQUlzLmZpbmRJbmRleCgNCiAgICAgICAgKGFpKSA9PiBhaS5uYW1lID09PSAi5oqV6YCS5Yiw55+l5LmOIg0KICAgICAgKTsNCiAgICAgIGlmIChleGlzdEluZGV4ID09PSAtMSkgew0KICAgICAgICB0aGlzLmVuYWJsZWRBSXMudW5zaGlmdCh6aGlodUFJKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZW5hYmxlZEFJc1tleGlzdEluZGV4XSA9IHpoaWh1QUk7DQogICAgICAgIGNvbnN0IHpoaWh1ID0gdGhpcy5lbmFibGVkQUlzLnNwbGljZShleGlzdEluZGV4LCAxKVswXTsNCiAgICAgICAgdGhpcy5lbmFibGVkQUlzLnVuc2hpZnQoemhpaHUpOw0KICAgICAgfQ0KDQogICAgICAvLyDlj5HpgIHnn6XkuY7mipXpgJLor7fmsYINCiAgICAgIGNvbnN0IHpoaWh1UmVxdWVzdCA9IHsNCiAgICAgICAganNvbnJwYzogIjIuMCIsDQogICAgICAgIGlkOiB1dWlkdjQoKSwNCiAgICAgICAgbWV0aG9kOiAi5oqV6YCS5Yiw55+l5LmOIiwNCiAgICAgICAgcGFyYW1zOiB7DQogICAgICAgICAgdGFza0lkOiB1dWlkdjQoKSwNCiAgICAgICAgICB1c2VySWQ6IHRoaXMudXNlcklkLA0KICAgICAgICAgIGNvcnBJZDogdGhpcy5jb3JwSWQsDQogICAgICAgICAgdXNlclByb21wdDogdGhpcy5sYXlvdXRQcm9tcHQsDQogICAgICAgICAgcm9sZXM6ICIiLA0KICAgICAgICAgIHNlbGVjdGVkTWVkaWE6ICJ6aGlodSIsDQogICAgICAgICAgY29udGVudFRleHQ6IHRoaXMuY3VycmVudExheW91dFJlc3VsdC5jb250ZW50LA0KICAgICAgICAgIHNoYXJlVXJsOiB0aGlzLmN1cnJlbnRMYXlvdXRSZXN1bHQuc2hhcmVVcmwsDQogICAgICAgICAgYWlOYW1lOiB0aGlzLmN1cnJlbnRMYXlvdXRSZXN1bHQuYWlOYW1lLA0KICAgICAgICB9LA0KICAgICAgfTsNCg0KICAgICAgY29uc29sZS5sb2coIuefpeS5juaKlemAkuWPguaVsCIsIHpoaWh1UmVxdWVzdCk7DQogICAgICB0aGlzLm1lc3NhZ2UoemhpaHVSZXF1ZXN0KTsNCiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCk7DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuefpeS5juaKlemAkuS7u+WKoeW3suWIm+W7uu+8jOato+WcqOWkhOeQhi4uLiIpOw0KICAgIH0sDQogICAgLy8g5Yib5bu655m+5a625Y+35oqV6YCS5Lu75Yqh77yI54us56uL5Lu75Yqh77yJDQogICAgY3JlYXRlQmFpamlhaGFvTGF5b3V0VGFzaygpIHsNCiAgICAgIGNvbnN0IGJhaWppYWhhb0FJID0gew0KICAgICAgICBuYW1lOiAi5oqV6YCS5Yiw55m+5a625Y+3IiwNCiAgICAgICAgYXZhdGFyOiByZXF1aXJlKCIuLi8uLi8uLi9hc3NldHMvYWkveXVhbmJhby5wbmciKSwNCiAgICAgICAgY2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgc2VsZWN0ZWRDYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICBlbmFibGVkOiB0cnVlLA0KICAgICAgICBzdGF0dXM6ICJydW5uaW5nIiwNCiAgICAgICAgcHJvZ3Jlc3NMb2dzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgY29udGVudDogIueZvuWutuWPt+aKlemAkuS7u+WKoeW3suWIm+W7uu+8jOato+WcqOWHhuWkh+WGheWuueaOkueJiC4uLiIsDQogICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksDQogICAgICAgICAgICBpc0NvbXBsZXRlZDogZmFsc2UsDQogICAgICAgICAgICB0eXBlOiAi5oqV6YCS5Yiw55m+5a625Y+3IiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBpc0V4cGFuZGVkOiB0cnVlLA0KICAgICAgfTsNCg0KICAgICAgLy8g5qOA5p+l5piv5ZCm5bey5a2Y5Zyo55m+5a625Y+35oqV6YCS5Lu75YqhDQogICAgICBjb25zdCBleGlzdEluZGV4ID0gdGhpcy5lbmFibGVkQUlzLmZpbmRJbmRleCgNCiAgICAgICAgKGFpKSA9PiBhaS5uYW1lID09PSAi5oqV6YCS5Yiw55m+5a625Y+3Ig0KICAgICAgKTsNCiAgICAgIGlmIChleGlzdEluZGV4ID09PSAtMSkgew0KICAgICAgICB0aGlzLmVuYWJsZWRBSXMudW5zaGlmdChiYWlqaWFoYW9BSSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmVuYWJsZWRBSXNbZXhpc3RJbmRleF0gPSBiYWlqaWFoYW9BSTsNCiAgICAgICAgY29uc3QgYmFpamlhaGFvID0gdGhpcy5lbmFibGVkQUlzLnNwbGljZShleGlzdEluZGV4LCAxKVswXTsNCiAgICAgICAgdGhpcy5lbmFibGVkQUlzLnVuc2hpZnQoYmFpamlhaGFvQUkpOw0KICAgICAgfQ0KDQogICAgICAvLyDlj5HpgIHnmb7lrrblj7fmipXpgJLor7fmsYINCiAgICAgIGNvbnN0IGJhaWppYWhhb1JlcXVlc3QgPSB7DQogICAgICAgIGpzb25ycGM6ICIyLjAiLA0KICAgICAgICBpZDogdXVpZHY0KCksDQogICAgICAgIG1ldGhvZDogIuaKlemAkuWIsOeZvuWutuWPtyIsDQogICAgICAgIHBhcmFtczogew0KICAgICAgICAgIHRhc2tJZDogdXVpZHY0KCksDQogICAgICAgICAgdXNlcklkOiB0aGlzLnVzZXJJZCwNCiAgICAgICAgICBjb3JwSWQ6IHRoaXMuY29ycElkLA0KICAgICAgICAgIHVzZXJQcm9tcHQ6IHRoaXMubGF5b3V0UHJvbXB0LA0KICAgICAgICAgIHJvbGVzOiAiIiwNCiAgICAgICAgICBzZWxlY3RlZE1lZGlhOiAiYmFpamlhaGFvIiwNCiAgICAgICAgICBjb250ZW50VGV4dDogdGhpcy5jdXJyZW50TGF5b3V0UmVzdWx0LmNvbnRlbnQsDQogICAgICAgICAgc2hhcmVVcmw6IHRoaXMuY3VycmVudExheW91dFJlc3VsdC5zaGFyZVVybCwNCiAgICAgICAgICBhaU5hbWU6IHRoaXMuY3VycmVudExheW91dFJlc3VsdC5haU5hbWUsDQogICAgICAgIH0sDQogICAgICB9Ow0KDQogICAgICBjb25zb2xlLmxvZygi55m+5a625Y+35oqV6YCS5Y+C5pWwIiwgYmFpamlhaGFvUmVxdWVzdCk7DQogICAgICB0aGlzLm1lc3NhZ2UoYmFpamlhaGFvUmVxdWVzdCk7DQogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpOw0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLnmb7lrrblj7fmipXpgJLku7vliqHlt7LliJvlu7rvvIzmraPlnKjlpITnkIYuLi4iKTsNCiAgICB9LA0KICAgICAgLy8g5Yib5bu65YWs5LyX5Y+35o6S54mI5Lu75Yqh77yI5L+d5oyB5Y6f5pyJ6YC76L6R77yJDQogICAgICBjcmVhdGVXZWNoYXRMYXlvdXRUYXNrKCkgew0KICAgICAgICBjb25zdCBsYXlvdXRSZXF1ZXN0ID0gew0KICAgICAgICAgIGpzb25ycGM6ICIyLjAiLA0KICAgICAgICAgIGlkOiB1dWlkdjQoKSwNCiAgICAgICAgICBtZXRob2Q6ICJBSeaOkueJiCIsDQogICAgICAgICAgcGFyYW1zOiB7DQogICAgICAgICAgICB0YXNrSWQ6IHV1aWR2NCgpLA0KICAgICAgICAgICAgdXNlcklkOiB0aGlzLnVzZXJJZCwNCiAgICAgICAgICAgIGNvcnBJZDogdGhpcy5jb3JwSWQsDQogICAgICAgICAgICB1c2VyUHJvbXB0OiB0aGlzLmxheW91dFByb21wdCwNCiAgICAgICAgICAgIHJvbGVzOiAiIiwNCiAgICAgICAgICAgIHNlbGVjdGVkTWVkaWE6ICJ3ZWNoYXQiLA0KICAgICAgICAgIH0sDQogICAgICAgIH07DQoNCiAgICAgICAgY29uc29sZS5sb2coIuWFrOS8l+WPt+aOkueJiOWPguaVsCIsIGxheW91dFJlcXVlc3QpOw0KICAgICAgICB0aGlzLm1lc3NhZ2UobGF5b3V0UmVxdWVzdCk7DQoNCiAgICAgICAgY29uc3Qgem5wYkFJID0gew0KICAgICAgICAgIG5hbWU6ICLmmbrog73mjpLniYgiLA0KICAgICAgICAgIGF2YXRhcjogcmVxdWlyZSgiLi4vLi4vLi4vYXNzZXRzL2FpL3l1YW5iYW8ucG5nIiksDQogICAgICAgICAgY2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgICBzZWxlY3RlZENhcGFiaWxpdGllczogW10sDQogICAgICAgICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgICBzdGF0dXM6ICJydW5uaW5nIiwNCiAgICAgICAgICBwcm9ncmVzc0xvZ3M6IFsNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgY29udGVudDogIuaZuuiDveaOkueJiOS7u+WKoeW3suaPkOS6pO+8jOato+WcqOaOkueJiC4uLiIsDQogICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSwNCiAgICAgICAgICAgICAgaXNDb21wbGV0ZWQ6IGZhbHNlLA0KICAgICAgICAgICAgICB0eXBlOiAi5pm66IO95o6S54mIIiwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgXSwNCiAgICAgICAgICBpc0V4cGFuZGVkOiB0cnVlLA0KICAgICAgICB9Ow0KDQogICAgICAgIC8vIOajgOafpeaYr+WQpuW3suWtmOWcqOaZuuiDveaOkueJiOS7u+WKoQ0KICAgICAgICBjb25zdCBleGlzdEluZGV4ID0gdGhpcy5lbmFibGVkQUlzLmZpbmRJbmRleCgNCiAgICAgICAgICAoYWkpID0+IGFpLm5hbWUgPT09ICLmmbrog73mjpLniYgiDQogICAgICAgICk7DQogICAgICAgIGlmIChleGlzdEluZGV4ID09PSAtMSkgew0KICAgICAgICAgIHRoaXMuZW5hYmxlZEFJcy51bnNoaWZ0KHpucGJBSSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5lbmFibGVkQUlzW2V4aXN0SW5kZXhdID0gem5wYkFJOw0KICAgICAgICAgIGNvbnN0IHpucGIgPSB0aGlzLmVuYWJsZWRBSXMuc3BsaWNlKGV4aXN0SW5kZXgsIDEpWzBdOw0KICAgICAgICAgIHRoaXMuZW5hYmxlZEFJcy51bnNoaWZ0KHpucGIpOw0KICAgICAgICB9DQoNCiAgICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmjpLniYjor7fmsYLlt7Llj5HpgIHvvIzor7fnrYnlvoXnu5PmnpwiKTsNCiAgICAgIH0sDQoNCiAgICAvLyDliJvlu7rlvq7lpLTmnaHmjpLniYjku7vliqENCiAgICBjcmVhdGVUb3V0aWFvTGF5b3V0VGFzaygpIHsNCiAgICAgIC8vIOiOt+WPluaZuuiDveivhOWIhuWGheWuuQ0KICAgICAgY29uc3Qgc2NvcmVSZXN1bHQgPSB0aGlzLnJlc3VsdHMuZmluZChyID0+IHIuYWlOYW1lID09PSAn5pm66IO96K+E5YiGJyk7DQogICAgICBjb25zdCBzY29yZUNvbnRlbnQgPSBzY29yZVJlc3VsdCA/IHNjb3JlUmVzdWx0LmNvbnRlbnQgOiAnJzsNCg0KICAgICAgY29uc3QgbGF5b3V0UmVxdWVzdCA9IHsNCiAgICAgICAganNvbnJwYzogIjIuMCIsDQogICAgICAgIGlkOiB1dWlkdjQoKSwNCiAgICAgICAgbWV0aG9kOiAi5b6u5aS05p2h5o6S54mIIiwNCiAgICAgICAgcGFyYW1zOiB7DQogICAgICAgICAgdGFza0lkOiB1dWlkdjQoKSwNCiAgICAgICAgICB1c2VySWQ6IHRoaXMudXNlcklkLA0KICAgICAgICAgIGNvcnBJZDogdGhpcy5jb3JwSWQsDQogICAgICAgICAgdXNlclByb21wdDogYCR7c2NvcmVDb250ZW50fVxuJHt0aGlzLmxheW91dFByb21wdH1gLA0KICAgICAgICAgIHJvbGVzOiAiIiwNCiAgICAgICAgfSwNCiAgICAgIH07DQoNCiAgICAgIGNvbnNvbGUubG9nKCLlvq7lpLTmnaHmjpLniYjlj4LmlbAiLCBsYXlvdXRSZXF1ZXN0KTsNCiAgICAgIHRoaXMubWVzc2FnZShsYXlvdXRSZXF1ZXN0KTsNCg0KICAgICAgY29uc3QgdHRocGJBSSA9IHsNCiAgICAgICAgbmFtZTogIuW+ruWktOadoeaOkueJiCIsDQogICAgICAgIGF2YXRhcjogcmVxdWlyZSgiLi4vLi4vLi4vYXNzZXRzL2FpL3l1YW5iYW8ucG5nIiksDQogICAgICAgIGNhcGFiaWxpdGllczogW10sDQogICAgICAgIHNlbGVjdGVkQ2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgc3RhdHVzOiAicnVubmluZyIsDQogICAgICAgIHByb2dyZXNzTG9nczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGNvbnRlbnQ6ICLlvq7lpLTmnaHmjpLniYjku7vliqHlt7Lmj5DkuqTvvIzmraPlnKjmjpLniYguLi4iLA0KICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLA0KICAgICAgICAgICAgaXNDb21wbGV0ZWQ6IGZhbHNlLA0KICAgICAgICAgICAgdHlwZTogIuW+ruWktOadoeaOkueJiCIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgaXNFeHBhbmRlZDogdHJ1ZSwNCiAgICAgIH07DQoNCiAgICAgIC8vIOajgOafpeaYr+WQpuW3suWtmOWcqOW+ruWktOadoeaOkueJiOS7u+WKoQ0KICAgICAgY29uc3QgZXhpc3RJbmRleCA9IHRoaXMuZW5hYmxlZEFJcy5maW5kSW5kZXgoDQogICAgICAgIChhaSkgPT4gYWkubmFtZSA9PT0gIuW+ruWktOadoeaOkueJiCINCiAgICAgICk7DQogICAgICBpZiAoZXhpc3RJbmRleCA9PT0gLTEpIHsNCiAgICAgICAgdGhpcy5lbmFibGVkQUlzLnVuc2hpZnQodHRocGJBSSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmVuYWJsZWRBSXNbZXhpc3RJbmRleF0gPSB0dGhwYkFJOw0KICAgICAgICBjb25zdCB0dGhwYiA9IHRoaXMuZW5hYmxlZEFJcy5zcGxpY2UoZXhpc3RJbmRleCwgMSlbMF07DQogICAgICAgIHRoaXMuZW5hYmxlZEFJcy51bnNoaWZ0KHR0aHBiKTsNCiAgICAgIH0NCg0KICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5b6u5aS05p2h5o6S54mI6K+35rGC5bey5Y+R6YCB77yM6K+3562J5b6F57uT5p6cIik7DQogICAgICB9LA0KDQogICAgLy8g5a6e6ZmF5oqV6YCS5Yiw5YWs5LyX5Y+3DQogICAgcHVzaFRvV2VjaGF0V2l0aENvbnRlbnQoY29udGVudFRleHQpIHsNCiAgICAgIGlmICh0aGlzLnB1c2hpbmdUb1dlY2hhdCkgcmV0dXJuOw0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLlvIDlp4vmipXpgJLlhazkvJflj7fvvIEiKTsNCiAgICAgIHRoaXMucHVzaGluZ1RvV2VjaGF0ID0gdHJ1ZTsNCiAgICAgIHRoaXMucHVzaE9mZmljZU51bSArPSAxOw0KDQogICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgIGNvbnRlbnRUZXh0OiBjb250ZW50VGV4dCwNCiAgICAgICAgc2hhcmVVcmw6IHRoaXMuY3VycmVudExheW91dFJlc3VsdC5zaGFyZVVybCwNCiAgICAgICAgdXNlcklkOiB0aGlzLnVzZXJJZCwNCiAgICAgICAgbnVtOiB0aGlzLnB1c2hPZmZpY2VOdW0sDQogICAgICAgIGFpTmFtZTogdGhpcy5jdXJyZW50TGF5b3V0UmVzdWx0LmFpTmFtZSwNCiAgICAgIH07DQoNCiAgICAgIHB1c2hBdXRvT2ZmaWNlKHBhcmFtcykNCiAgICAgICAgLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaKlemAkuWIsOWFrOS8l+WPt+aIkOWKn++8gSIpOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cgfHwgIuaKlemAkuWksei0pe+8jOivt+mHjeivlSIpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKChlcnJvcikgPT4gew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuaKlemAkuWIsOWFrOS8l+WPt+Wksei0pToiLCBlcnJvcik7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5oqV6YCS5aSx6LSl77yM6K+36YeN6K+VIik7DQogICAgICAgIH0pDQogICAgICAgIC5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgICB0aGlzLnB1c2hpbmdUb1dlY2hhdCA9IGZhbHNlOw0KICAgICAgICB9KTsNCiAgICB9LA0KDQoNCg0KICAgIC8vIOehruiupOW+ruWktOadoeWPkeW4gw0KICAgIGNvbmZpcm1UVEhQdWJsaXNoKCkgew0KICAgICAgaWYgKCF0aGlzLnR0aEFydGljbGVUaXRsZSB8fCAhdGhpcy50dGhBcnRpY2xlQ29udGVudCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+Whq+WGmeagh+mimOWSjOWGheWuuScpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICAvLyDmnoTlu7rlvq7lpLTmnaHlj5HluIPor7fmsYINCiAgICAgIGNvbnN0IHB1Ymxpc2hSZXF1ZXN0ID0gew0KICAgICAgICBqc29ucnBjOiAnMi4wJywNCiAgICAgICAgaWQ6IHV1aWR2NCgpLA0KICAgICAgICAgICAgICAgICAgbWV0aG9kOiAn5b6u5aS05p2h5Y+R5biDJywNCiAgICAgICAgcGFyYW1zOiB7DQogICAgICAgICAgdGFza0lkOiB1dWlkdjQoKSwNCiAgICAgICAgICB1c2VySWQ6IHRoaXMudXNlcklkLA0KICAgICAgICAgIGNvcnBJZDogdGhpcy5jb3JwSWQsDQogICAgICAgICAgcm9sZXM6ICcnLA0KICAgICAgICAgIHRpdGxlOiB0aGlzLnR0aEFydGljbGVUaXRsZSwNCiAgICAgICAgICBjb250ZW50OiB0aGlzLnR0aEFydGljbGVDb250ZW50LA0KICAgICAgICAgIHR5cGU6ICflvq7lpLTmnaHlj5HluIMnDQogICAgICAgIH0NCiAgICAgIH07DQogICAgICAvLyDlj5HpgIHlj5HluIPor7fmsYINCiAgICAgIGNvbnNvbGUubG9nKCLlvq7lpLTmnaHlj5HluIPlj4LmlbAiLCBwdWJsaXNoUmVxdWVzdCk7DQogICAgICB0aGlzLm1lc3NhZ2UocHVibGlzaFJlcXVlc3QpOw0KICAgICAgdGhpcy50dGhBcnRpY2xlRWRpdFZpc2libGUgPSBmYWxzZTsNCiAgICAgIC8vIOaYvuekuua1geeoi+W8ueeqlw0KICAgICAgdGhpcy50dGhGbG93VmlzaWJsZSA9IHRydWU7DQogICAgICB0aGlzLnR0aEZsb3dMb2dzID0gW107DQogICAgICB0aGlzLnR0aEZsb3dJbWFnZXMgPSBbXTsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5b6u5aS05p2h5Y+R5biD6K+35rGC5bey5Y+R6YCB77yBJyk7DQogICAgfSwNCg0KDQogICAgLy8g5YWz6Zet5b6u5aS05p2h5Y+R5biD5rWB56iL5by556qXDQogICAgY2xvc2VUVEhGbG93RGlhbG9nKCkgew0KICAgICAgdGhpcy50dGhGbG93VmlzaWJsZSA9IGZhbHNlOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAulBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/wechat/chrome", "sourcesContent": ["<template>\r\n  <div class=\"ai-management-platform\">\r\n    <!-- 顶部导航区 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"logo-area\">\r\n        <img src=\"../../../assets/ai/logo.png\" alt=\"Logo\" class=\"logo\" />\r\n        <h1 class=\"platform-title\">主机</h1>\r\n      </div>\r\n      <div class=\"nav-buttons\">\r\n        <el-button type=\"primary\" size=\"small\" @click=\"createNewChat\">\r\n          <i class=\"el-icon-plus\"></i>\r\n          创建新对话\r\n        </el-button>\r\n        <div class=\"history-button\">\r\n          <el-button type=\"text\" @click=\"showHistoryDrawer\">\r\n            <img\r\n              :src=\"require('../../../assets/ai/celan.png')\"\r\n              alt=\"历史记录\"\r\n              class=\"history-icon\"\r\n            />\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 历史记录抽屉 -->\r\n    <el-drawer\r\n      title=\"历史会话记录\"\r\n      :visible.sync=\"historyDrawerVisible\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      :before-close=\"handleHistoryDrawerClose\"\r\n    >\r\n      <div class=\"history-content\">\r\n        <div\r\n          v-for=\"(group, date) in groupedHistory\"\r\n          :key=\"date\"\r\n          class=\"history-group\"\r\n        >\r\n          <div class=\"history-date\">{{ date }}</div>\r\n          <div class=\"history-list\">\r\n            <div\r\n              v-for=\"(item, index) in group\"\r\n              :key=\"index\"\r\n              class=\"history-item\"\r\n            >\r\n              <div class=\"history-parent\" @click=\"loadHistoryItem(item)\">\r\n                <div class=\"history-header\">\r\n                  <i\r\n                    :class=\"[\r\n                      'el-icon-arrow-right',\r\n                      { 'is-expanded': item.isExpanded },\r\n                    ]\"\r\n                    @click.stop=\"toggleHistoryExpansion(item)\"\r\n                  ></i>\r\n                  <div class=\"history-prompt\">{{ item.userPrompt }}</div>\r\n                </div>\r\n                <div class=\"history-time\">\r\n                  {{ formatHistoryTime(item.createTime) }}\r\n                </div>\r\n              </div>\r\n              <div\r\n                v-if=\"\r\n                  item.children && item.children.length > 0 && item.isExpanded\r\n                \"\r\n                class=\"history-children\"\r\n              >\r\n                <div\r\n                  v-for=\"(child, childIndex) in item.children\"\r\n                  :key=\"childIndex\"\r\n                  class=\"history-child-item\"\r\n                  @click=\"loadHistoryItem(child)\"\r\n                >\r\n                  <div class=\"history-prompt\">{{ child.userPrompt }}</div>\r\n                  <div class=\"history-time\">\r\n                    {{ formatHistoryTime(child.createTime) }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <div class=\"main-content\">\r\n      <el-collapse v-model=\"activeCollapses\">\r\n        <el-collapse-item title=\"AI选择配置\" name=\"ai-selection\">\r\n          <div class=\"ai-selection-section\">\r\n            <div class=\"ai-cards\">\r\n              <el-card\r\n                v-for=\"(ai, index) in aiList\"\r\n                :key=\"index\"\r\n                class=\"ai-card\"\r\n                shadow=\"hover\"\r\n              >\r\n                <div class=\"ai-card-header\">\r\n                  <div class=\"ai-left\">\r\n                    <div class=\"ai-avatar\">\r\n                      <img :src=\"ai.avatar\" alt=\"AI头像\" />\r\n                    </div>\r\n                    <div class=\"ai-name\">{{ ai.name }}</div>\r\n                  </div>\r\n                  <div class=\"ai-status\">\r\n                    <el-switch\r\n                      v-model=\"ai.enabled\"\r\n                      active-color=\"#13ce66\"\r\n                      inactive-color=\"#ff4949\"\r\n                    >\r\n                    </el-switch>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ai-capabilities\" v-if=\"ai.capabilities && ai.capabilities.length > 0\">\r\n                  <!-- 通义只支持单选-->\r\n                  <div v-if=\"ai.name === '通义千问'\" class=\"button-capability-group\">\r\n                    <el-button\r\n                      v-for=\"capability in ai.capabilities\"\r\n                      :key=\"capability.value\" size=\"mini\"\r\n                      :type=\"ai.selectedCapability === capability.value ? 'primary' : 'info'\"\r\n                      :disabled=\"!ai.enabled\"\r\n                      :plain=\"ai.selectedCapability !== capability.value\"\r\n                      @click=\"selectSingleCapability(ai, capability.value)\"\r\n                      class=\"capability-button\"\r\n                    >\r\n                      {{ capability.label }}\r\n                    </el-button>\r\n                  </div>\r\n                  <!-- 其他AI -->\r\n                  <div v-else class=\"button-capability-group\">\r\n                    <el-button\r\n                      v-for=\"capability in ai.capabilities\"\r\n                      :key=\"capability.value\"\r\n                      size=\"mini\"\r\n                      :type=\"ai.selectedCapabilities.includes(capability.value) ? 'primary' : 'info'\"\r\n                      :disabled=\"!ai.enabled\"\r\n                      :plain=\"!ai.selectedCapabilities.includes(capability.value)\"\r\n                      @click=\"toggleCapability(ai, capability.value)\"\r\n                      class=\"capability-button\"\r\n                    >\r\n                      {{ capability.label }}\r\n                    </el-button>\r\n                  </div>\r\n                </div>\r\n              </el-card>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n\r\n        <!-- 提示词输入区 -->\r\n        <el-collapse-item title=\"提示词输入\" name=\"prompt-input\">\r\n          <div class=\"prompt-input-section\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              :rows=\"5\"\r\n              placeholder=\"请输入提示词，支持Markdown格式\"\r\n              v-model=\"promptInput\"\r\n              resize=\"none\"\r\n              class=\"prompt-input\"\r\n            >\r\n            </el-input>\r\n            <div class=\"prompt-footer\">\r\n              <div class=\"word-count\">字数统计: {{ promptInput.length }}</div>\r\n              <el-button\r\n                type=\"primary\"\r\n                @click=\"sendPrompt\"\r\n                :disabled=\"!canSend\"\r\n                class=\"send-button\"\r\n              >\r\n                发送\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n      </el-collapse>\r\n\r\n      <!-- 执行状态展示区 -->\r\n      <div class=\"execution-status-section\" v-if=\"taskStarted\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"task-flow-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>任务流程</span>\r\n              </div>\r\n              <div class=\"task-flow\">\r\n                <div\r\n                  v-for=\"(ai, index) in enabledAIs\"\r\n                  :key=\"index\"\r\n                  class=\"task-item\"\r\n                >\r\n                  <div class=\"task-header\" @click=\"toggleAIExpansion(ai)\">\r\n                    <div class=\"header-left\">\r\n                      <i\r\n                        :class=\"[\r\n                          'el-icon-arrow-right',\r\n                          { 'is-expanded': ai.isExpanded },\r\n                        ]\"\r\n                      ></i>\r\n                      <span class=\"ai-name\">{{ ai.name }}</span>\r\n                    </div>\r\n                    <div class=\"header-right\">\r\n                      <span class=\"status-text\">{{\r\n                        getStatusText(ai.status)\r\n                      }}</span>\r\n                      <i\r\n                        :class=\"getStatusIcon(ai.status)\"\r\n                        class=\"status-icon\"\r\n                      ></i>\r\n                    </div>\r\n                  </div>\r\n                  <!-- 添加进度轨迹 -->\r\n                  <div\r\n                    class=\"progress-timeline\"\r\n                    v-if=\"ai.progressLogs.length > 0 && ai.isExpanded\"\r\n                  >\r\n                    <div class=\"timeline-scroll\">\r\n                      <div\r\n                        v-for=\"(log, logIndex) in ai.progressLogs\"\r\n                        :key=\"logIndex\"\r\n                        class=\"progress-item\"\r\n                        :class=\"{\r\n                          completed: log.isCompleted || logIndex > 0,\r\n                          current: !log.isCompleted && logIndex === 0,\r\n                        }\"\r\n                      >\r\n                        <div class=\"progress-dot\"></div>\r\n                        <div\r\n                          class=\"progress-line\"\r\n                          v-if=\"logIndex < ai.progressLogs.length - 1\"\r\n                        ></div>\r\n                        <div class=\"progress-content\">\r\n                          <div class=\"progress-time\">\r\n                            {{ formatTime(log.timestamp) }}\r\n                          </div>\r\n                          <div class=\"progress-text\">{{ log.content }}</div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"screenshots-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>主机可视化</span>\r\n                <div class=\"controls\">\r\n                  <el-switch\r\n                    v-model=\"autoPlay\"\r\n                    active-text=\"自动轮播\"\r\n                    inactive-text=\"手动切换\"\r\n                  >\r\n                  </el-switch>\r\n                </div>\r\n              </div>\r\n              <div class=\"screenshots\">\r\n                <el-carousel\r\n                  :interval=\"3000\"\r\n                  :autoplay=\"false\"\r\n                  indicator-position=\"outside\"\r\n                  height=\"700px\"\r\n                >\r\n                  <el-carousel-item\r\n                    v-for=\"(screenshot, index) in screenshots\"\r\n                    :key=\"index\"\r\n                  >\r\n                    <img\r\n                      :src=\"screenshot\"\r\n                      alt=\"执行截图\"\r\n                      class=\"screenshot-image\"\r\n                      @click=\"showLargeImage(screenshot)\"\r\n                    />\r\n                  </el-carousel-item>\r\n                </el-carousel>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 结果展示区 -->\r\n      <div class=\"results-section\" v-if=\"results.length > 0\">\r\n        <div class=\"section-header\">\r\n          <h2 class=\"section-title\">执行结果</h2>\r\n          <el-button type=\"primary\" @click=\"showScoreDialog\" size=\"small\">\r\n            智能评分\r\n          </el-button>\r\n        </div>\r\n        <el-tabs v-model=\"activeResultTab\" type=\"card\">\r\n          <el-tab-pane\r\n            v-for=\"(result, index) in results\"\r\n            :key=\"index\"\r\n            :label=\"result.aiName\"\r\n            :name=\"'result-' + index\"\r\n          >\r\n            <div class=\"result-content\">\r\n              <div class=\"result-header\" v-if=\"result.shareUrl\">\r\n                <div class=\"result-title\">{{ result.aiName }}的执行结果</div>\r\n                <div class=\"result-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-link\"\r\n                    @click=\"openShareUrl(result.shareUrl)\"\r\n                    class=\"share-link-btn\"\r\n                  >\r\n                    查看原链接\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"success\"\r\n                    icon=\"el-icon-s-promotion\"\r\n                    @click=\"handlePushToMedia(result)\"\r\n                    class=\"push-media-btn\"\r\n                    :loading=\"pushingToMedia\"\r\n                    :disabled=\"pushingToMedia\"\r\n                  >\r\n                    投递到媒体\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <!-- 如果有shareImgUrl则渲染图片或PDF，否则渲染markdown -->\r\n              <div v-if=\"result.shareImgUrl\" class=\"share-content\">\r\n                <!-- 渲染图片 -->\r\n                <img\r\n                  v-if=\"isImageFile(result.shareImgUrl)\"\r\n                  :src=\"result.shareImgUrl\"\r\n                  alt=\"分享图片\"\r\n                  class=\"share-image\"\r\n                  :style=\"getImageStyle(result.aiName)\"\r\n                />\r\n                <!-- 渲染PDF -->\r\n                <iframe\r\n                  v-else-if=\"isPdfFile(result.shareImgUrl)\"\r\n                  :src=\"result.shareImgUrl\"\r\n                  class=\"share-pdf\"\r\n                  frameborder=\"0\"\r\n                >\r\n                </iframe>\r\n                <!-- 其他文件类型显示链接 -->\r\n                <div v-else class=\"share-file\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-document\"\r\n                    @click=\"openShareUrl(result.shareImgUrl)\"\r\n                  >\r\n                    查看文件\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <div\r\n                v-else\r\n                class=\"markdown-content\"\r\n                v-html=\"renderMarkdown(result.content)\"\r\n              ></div>\r\n              <div class=\"action-buttons\">\r\n                <el-button\r\n                  size=\"small\"\r\n                  type=\"primary\"\r\n                  @click=\"copyResult(result.content)\"\r\n                  >复制（纯文本）</el-button\r\n                >\r\n                <el-button\r\n                  size=\"small\"\r\n                  type=\"success\"\r\n                  @click=\"exportResult(result)\"\r\n                  >导出（MD文件）</el-button\r\n                >\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 大图查看对话框 -->\r\n    <el-dialog\r\n      :visible.sync=\"showImageDialog\"\r\n      width=\"90%\"\r\n      :show-close=\"true\"\r\n      :modal=\"true\"\r\n      center\r\n      class=\"image-dialog\"\r\n      :append-to-body=\"true\"\r\n      @close=\"closeLargeImage\"\r\n    >\r\n      <div class=\"large-image-container\">\r\n        <!-- 如果是单张分享图片，直接显示 -->\r\n        <div\r\n          v-if=\"currentLargeImage && !screenshots.includes(currentLargeImage)\"\r\n          class=\"single-image-container\"\r\n        >\r\n          <img :src=\"currentLargeImage\" alt=\"大图\" class=\"large-image\" />\r\n        </div>\r\n        <!-- 如果是截图轮播 -->\r\n        <el-carousel\r\n          v-else\r\n          :interval=\"3000\"\r\n          :autoplay=\"false\"\r\n          indicator-position=\"outside\"\r\n          height=\"80vh\"\r\n        >\r\n          <el-carousel-item\r\n            v-for=\"(screenshot, index) in screenshots\"\r\n            :key=\"index\"\r\n          >\r\n            <img :src=\"screenshot\" alt=\"大图\" class=\"large-image\" />\r\n          </el-carousel-item>\r\n        </el-carousel>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 评分弹窗 -->\r\n    <el-dialog\r\n      title=\"智能评分\"\r\n      :visible.sync=\"scoreDialogVisible\"\r\n      width=\"60%\"\r\n      height=\"65%\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"score-dialog\"\r\n    >\r\n      <div class=\"score-dialog-content\">\r\n        <div class=\"score-prompt-section\">\r\n          <h3>评分提示词：</h3>\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"10\"\r\n            placeholder=\"请输入评分提示词，例如：请从内容质量、逻辑性、创新性等方面进行评分\"\r\n            v-model=\"scorePrompt\"\r\n            resize=\"none\"\r\n            class=\"score-prompt-input\"\r\n          >\r\n          </el-input>\r\n        </div>\r\n        <div class=\"selected-results\">\r\n          <h3>选择要评分的内容：</h3>\r\n          <el-checkbox-group v-model=\"selectedResults\">\r\n            <el-checkbox\r\n              v-for=\"(result, index) in results\"\r\n              :key=\"index\"\r\n              :label=\"result.aiName\"\r\n              class=\"result-checkbox\"\r\n            >\r\n              {{ result.aiName }}\r\n            </el-checkbox>\r\n          </el-checkbox-group>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"scoreDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleScore\" :disabled=\"!canScore\">\r\n          开始评分\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 投递到媒体弹窗 -->\r\n    <el-dialog\r\n      title=\"媒体投递设置\"\r\n      :visible.sync=\"layoutDialogVisible\"\r\n      width=\"60%\"\r\n      height=\"65%\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"layout-dialog\"\r\n    >\r\n      <div class=\"layout-dialog-content\">\r\n        <!-- 媒体选择区域 -->\r\n        <div class=\"media-selection-section\">\r\n          <h3>选择投递媒体：</h3>\r\n          <el-radio-group v-model=\"selectedMedia\" size=\"small\" class=\"media-radio-group\">\r\n            <el-radio-button label=\"wechat\">\r\n              <i class=\"el-icon-chat-dot-square\"></i>\r\n              公众号\r\n            </el-radio-button>\r\n            <el-radio-button label=\"zhihu\">\r\n              <i class=\"el-icon-document\"></i>\r\n              知乎\r\n            </el-radio-button>\r\n            <el-radio-button label=\"toutiao\">\r\n              <i class=\"el-icon-edit-outline\"></i>\r\n              微头条\r\n            </el-radio-button>\r\n            <el-radio-button label=\"baijiahao\">\r\n              <i class=\"el-icon-edit-outline\"></i>\r\n              百家号\r\n            </el-radio-button>\r\n          </el-radio-group>\r\n          <div class=\"media-description\">\r\n            <template v-if=\"selectedMedia === 'wechat'\">\r\n              <small>📝 将内容排版为适合微信公众号的HTML格式，并自动投递到草稿箱</small>\r\n            </template>\r\n            <template v-else-if=\"selectedMedia === 'zhihu'\">\r\n              <small>📖 将内容转换为知乎专业文章格式，直接投递到知乎草稿箱</small>\r\n            </template>\r\n            <template v-else-if=\"selectedMedia === 'toutiao'\">\r\n              <small>📰 将内容转换为微头条文章格式，支持文章编辑和发布</small>\r\n            </template>\r\n            <template v-else-if=\"selectedMedia === 'toutiao'\">\r\n              <small>🔈 将内容转换为百家号帖子格式，直接投递到百家号草稿箱</small>\r\n            </template>\r\n          </div>\r\n        </div>\r\n\r\n\r\n        <div class=\"layout-prompt-section\">\r\n          <h3>排版提示词：</h3>\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"12\"\r\n            placeholder=\"请输入排版提示词\"\r\n            v-model=\"layoutPrompt\"\r\n            resize=\"none\"\r\n            class=\"layout-prompt-input\"\r\n          >\r\n          </el-input>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"layoutDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleLayout\" :disabled=\"!canLayout\">\r\n          排版后智能投递\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 微头条发布流程弹窗 -->\r\n    <el-dialog title=\"微头条发布流程\" :visible.sync=\"tthFlowVisible\" width=\"60%\" height=\"60%\" :close-on-click-modal=\"false\"\r\n      class=\"tth-flow-dialog\">\r\n      <div class=\"tth-flow-content\">\r\n        <div class=\"flow-logs-section\">\r\n          <h3>发布流程日志：</h3>\r\n          <div class=\"progress-timeline\">\r\n            <div class=\"timeline-scroll\">\r\n              <div v-for=\"(log, index) in tthFlowLogs\" :key=\"index\" class=\"progress-item completed\">\r\n                <div class=\"progress-dot\"></div>\r\n                <div v-if=\"index < tthFlowLogs.length - 1\" class=\"progress-line\"></div>\r\n                <div class=\"progress-content\">\r\n                  <div class=\"progress-time\">{{ formatTime(log.timestamp) }}</div>\r\n                  <div class=\"progress-text\">{{ log.content }}</div>\r\n                </div>\r\n              </div>\r\n              <div v-if=\"tthFlowLogs.length === 0\" class=\"no-logs\">暂无流程日志...</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"flow-images-section\">\r\n          <h3>发布流程图片：</h3>\r\n          <div class=\"flow-images-container\">\r\n            <template v-if=\"tthFlowImages.length > 0\">\r\n              <div v-for=\"(image, index) in tthFlowImages\" :key=\"index\" class=\"flow-image-item\">\r\n                <img :src=\"image\" alt=\"流程图片\" class=\"flow-image\" @click=\"showLargeImage(image)\">\r\n              </div>\r\n            </template>\r\n            <div v-else class=\"no-logs\">暂无流程图片...</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"closeTTHFlowDialog\">关闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 微头条文章编辑弹窗 -->\r\n    <el-dialog title=\"微头条文章编辑\" :visible.sync=\"tthArticleEditVisible\" width=\"70%\" height=\"80%\" :close-on-click-modal=\"false\"\r\n      class=\"tth-article-edit-dialog\">\r\n      <div class=\"tth-article-edit-content\">\r\n        <div class=\"article-title-section\">\r\n          <h3>文章标题：</h3>\r\n          <el-input v-model=\"tthArticleTitle\" placeholder=\"请输入文章标题\" class=\"article-title-input\"></el-input>\r\n        </div>\r\n        <div class=\"article-content-section\">\r\n          <h3>文章内容：</h3>\r\n          <div class=\"content-input-wrapper\">\r\n            <el-input \r\n              type=\"textarea\" \r\n              v-model=\"tthArticleContent\" \r\n              :rows=\"20\" \r\n              placeholder=\"请输入文章内容\"\r\n              resize=\"none\" \r\n              class=\"article-content-input\"\r\n              :class=\"{ 'content-over-limit': tthArticleContent.length > 2000 }\"\r\n            ></el-input>\r\n            <div class=\"content-length-info\" :class=\"{ 'text-danger': tthArticleContent.length > 2000 }\">\r\n              字数：{{ tthArticleContent.length }}/2000\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"tthArticleEditVisible = false\">关 闭</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmTTHPublish\" :disabled=\"!tthArticleTitle || !tthArticleContent\">\r\n          确定发布\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { marked } from \"marked\";\r\nimport {\r\n  message,\r\n  saveUserChatData,\r\n  getChatHistory,\r\n  pushAutoOffice,\r\n  getMediaCallWord,\r\n} from \"@/api/wechat/aigc\";\r\nimport { v4 as uuidv4 } from \"uuid\";\r\nimport websocketClient from \"@/utils/websocket\";\r\nimport store from \"@/store\";\r\nimport TurndownService from \"turndown\";\r\n\r\nexport default {\r\n  name: \"AIManagementPlatform\",\r\n  data() {\r\n    return {\r\n      userId: store.state.user.id,\r\n      corpId: store.state.user.corp_id,\r\n      chatId: uuidv4(),\r\n      expandedHistoryItems: {},\r\n      userInfoReq: {\r\n        userPrompt: \"\",\r\n        userId: \"\",\r\n        corpId: \"\",\r\n        taskId: \"\",\r\n        roles: \"\",\r\n        toneChatId: \"\",\r\n        ybDsChatId: \"\",\r\n        dbChatId: \"\",\r\n        tyChatId: \"\",\r\n        isNewChat: true,\r\n      },\r\n      jsonRpcReqest: {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"\",\r\n        params: {},\r\n      },\r\n      aiList: [\r\n        {\r\n          name: \"DeepSeek\",\r\n          avatar: require(\"../../../assets/logo/Deepseek.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网搜索\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [\"deep_thinking\", \"web_search\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"豆包\",\r\n          avatar: require(\"../../../assets/ai/豆包.png\"),\r\n          capabilities: [{ label: \"深度思考\", value: \"deep_thinking\" }],\r\n          selectedCapabilities: [\"deep_thinking\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"MiniMax Chat\",\r\n          avatar: require(\"../../../assets/ai/MiniMax.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网搜索\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: '通义千问',\r\n          avatar: require('../../../assets/ai/qw.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapability: '',\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        }\r\n      ],\r\n      promptInput: \"\",\r\n      taskStarted: false,\r\n      autoPlay: false,\r\n      screenshots: [],\r\n      results: [],\r\n      activeResultTab: \"result-0\",\r\n      activeCollapses: [\"ai-selection\", \"prompt-input\"], // 默认展开这两个区域\r\n      showImageDialog: false,\r\n      currentLargeImage: \"\",\r\n      enabledAIs: [],\r\n      turndownService: new TurndownService({\r\n        headingStyle: \"atx\",\r\n        codeBlockStyle: \"fenced\",\r\n        emDelimiter: \"*\",\r\n      }),\r\n      scoreDialogVisible: false,\r\n      selectedResults: [],\r\n      scorePrompt: `请你深度阅读以下几篇内容，从多个维度进行逐项打分，输出评分结果。并在以下各篇文章的基础上博采众长，综合整理一篇更全面的文章。`,\r\n      layoutDialogVisible: false,\r\n      layoutPrompt: \"\",\r\n      currentLayoutResult: null, // 当前要排版的结果\r\n      historyDrawerVisible: false,\r\n      chatHistory: [],\r\n      pushOfficeNum: 0, // 投递到公众号的递增编号\r\n      pushingToWechat: false, // 投递到公众号的loading状态\r\n      selectedMedia: \"wechat\", // 默认选择公众号\r\n      pushingToMedia: false, // 投递到媒体的loading状态\r\n      // 微头条相关变量\r\n      tthFlowVisible: false, // 微头条发布流程弹窗\r\n      tthFlowLogs: [], // 微头条发布流程日志\r\n      tthFlowImages: [], // 微头条发布流程图片\r\n      tthArticleEditVisible: false, // 微头条文章编辑弹窗\r\n      tthArticleTitle: '', // 微头条文章标题\r\n      tthArticleContent: '', // 微头条文章内容\r\n    };\r\n  },\r\n  computed: {\r\n    canSend() {\r\n      return (\r\n        this.promptInput.trim().length > 0 &&\r\n        this.aiList.some((ai) => ai.enabled)\r\n      );\r\n    },\r\n    canScore() {\r\n      return (\r\n        this.selectedResults.length > 0 && this.scorePrompt.trim().length > 0\r\n      );\r\n    },\r\n    canLayout() {\r\n      return this.layoutPrompt.trim().length > 0;\r\n    },\r\n    groupedHistory() {\r\n      const groups = {};\r\n      const chatGroups = {};\r\n\r\n      // 首先按chatId分组\r\n      this.chatHistory.forEach((item) => {\r\n        if (!chatGroups[item.chatId]) {\r\n          chatGroups[item.chatId] = [];\r\n        }\r\n        chatGroups[item.chatId].push(item);\r\n      });\r\n\r\n      // 然后按日期分组，并处理父子关系\r\n      Object.values(chatGroups).forEach((chatGroup) => {\r\n        // 按时间排序\r\n        chatGroup.sort(\r\n          (a, b) => new Date(a.createTime) - new Date(b.createTime)\r\n        );\r\n\r\n        // 获取最早的记录作为父级\r\n        const parentItem = chatGroup[0];\r\n        const date = this.getHistoryDate(parentItem.createTime);\r\n\r\n        if (!groups[date]) {\r\n          groups[date] = [];\r\n        }\r\n\r\n        // 添加父级记录\r\n        groups[date].push({\r\n          ...parentItem,\r\n          isParent: true,\r\n          isExpanded: this.expandedHistoryItems[parentItem.chatId] || false,\r\n          children: chatGroup.slice(1).map((child) => ({\r\n            ...child,\r\n            isParent: false,\r\n          })),\r\n        });\r\n      });\r\n\r\n      return groups;\r\n    },\r\n  },\r\n  created() {\r\n    console.log(\"页面初始化 - userId:\", this.userId);\r\n    console.log(\"页面初始化 - corpId:\", this.corpId);\r\n    this.initWebSocket(this.userId);\r\n    console.log(\"开始加载历史记录...\");\r\n    this.loadChatHistory(0); // 加载历史记录\r\n    console.log(\"开始加载上次会话...\");\r\n    this.loadLastChat(); // 加载上次会话\r\n  },\r\n  watch: {\r\n    // 监听媒体选择变化，自动加载对应的提示词\r\n    selectedMedia: {\r\n      handler(newMedia) {\r\n        this.loadMediaPrompt(newMedia);\r\n      },\r\n      immediate: false\r\n    }\r\n  },\r\n  methods: {\r\n    sendPrompt() {\r\n      if (!this.canSend) return;\r\n\r\n      this.screenshots = [];\r\n      // 折叠所有区域\r\n      this.activeCollapses = [];\r\n\r\n      this.taskStarted = true;\r\n      this.results = []; // 清空之前的结果\r\n\r\n      this.userInfoReq.roles = \"\";\r\n\r\n      this.userInfoReq.taskId = uuidv4();\r\n      this.userInfoReq.userId = this.userId;\r\n      this.userInfoReq.corpId = this.corpId;\r\n      this.userInfoReq.userPrompt = this.promptInput;\r\n\r\n      // 获取启用的AI列表及其状态\r\n      this.enabledAIs = this.aiList.filter((ai) => ai.enabled);\r\n\r\n      // 将所有启用的AI状态设置为运行中\r\n      this.enabledAIs.forEach((ai) => {\r\n        this.$set(ai, \"status\", \"running\");\r\n      });\r\n\r\n      this.enabledAIs.forEach((ai) => {\r\n        if (ai.name === \"DeepSeek\" && ai.enabled) {\r\n          this.userInfoReq.roles = this.userInfoReq.roles + \"deepseek,\";\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"ds-sdsk,\";\r\n          }\r\n          if (ai.selectedCapabilities.includes(\"web_search\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"ds-lwss,\";\r\n          }\r\n        }\r\n        if (ai.name === \"豆包\") {\r\n          this.userInfoReq.roles = this.userInfoReq.roles + \"zj-db,\";\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"zj-db-sdsk,\";\r\n          }\r\n        }\r\n        if (ai.name === \"MiniMax Chat\") {\r\n          this.userInfoReq.roles = this.userInfoReq.roles + \"mini-max-agent,\";\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"max-sdsk,\";\r\n          }\r\n          if (ai.selectedCapabilities.includes(\"web_search\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + \"max-lwss,\";\r\n          }\r\n        }\r\n        if(ai.name === '通义千问' && ai.enabled){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'ty-qw,';\r\n          if (ai.selectedCapability.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'ty-qw-sdsk,'\r\n          } else if (ai.selectedCapability.includes(\"web_search\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'ty-qw-lwss,';\r\n          }\r\n        }\r\n      });\r\n\r\n      console.log(\"参数：\", this.userInfoReq);\r\n\r\n      //调用后端接口\r\n      this.jsonRpcReqest.method = \"使用F8S\";\r\n      this.jsonRpcReqest.params = this.userInfoReq;\r\n      this.message(this.jsonRpcReqest);\r\n      this.userInfoReq.isNewChat = false;\r\n    },\r\n\r\n    message(data) {\r\n      message(data).then((res) => {\r\n        if (res.code == 201) {\r\n          this.$message.error(res.messages || '操作失败');\r\n        }\r\n      }).catch((error) => {\r\n        console.error(\"Message API调用失败:\", error);\r\n        // 这里不再显示错误消息，因为全局拦截器已经处理了\r\n      });\r\n    },\r\n    // 处理通义单选逻辑\r\n    selectSingleCapability(ai, capabilityValue) {\r\n      if (!ai.enabled) return;\r\n\r\n      if (ai.selectedCapability === capabilityValue) {\r\n        this.$set(ai, 'selectedCapability', '');\r\n      } else {\r\n        this.$set(ai, 'selectedCapability', capabilityValue);\r\n      }\r\n      this.$forceUpdate();\r\n    },\r\n    toggleCapability(ai, capabilityValue) {\r\n      if (!ai.enabled) return;\r\n\r\n      const index = ai.selectedCapabilities.indexOf(capabilityValue);\r\n      console.log(\"切换前:\", ai.selectedCapabilities);\r\n      if (index === -1) {\r\n        // 如果不存在，则添加\r\n        this.$set(\r\n          ai.selectedCapabilities,\r\n          ai.selectedCapabilities.length,\r\n          capabilityValue\r\n        );\r\n      } else {\r\n        // 如果已存在，则移除\r\n        const newCapabilities = [...ai.selectedCapabilities];\r\n        newCapabilities.splice(index, 1);\r\n        this.$set(ai, \"selectedCapabilities\", newCapabilities);\r\n      }\r\n      console.log(\"切换后:\", ai.selectedCapabilities);\r\n      this.$forceUpdate(); // 强制更新视图\r\n    },\r\n    getStatusText(status) {\r\n      switch (status) {\r\n        case \"idle\":\r\n          return \"等待中\";\r\n        case \"running\":\r\n          return \"正在执行\";\r\n        case \"completed\":\r\n          return \"已完成\";\r\n        case \"failed\":\r\n          return \"执行失败\";\r\n        default:\r\n          return \"未知状态\";\r\n      }\r\n    },\r\n    getStatusIcon(status) {\r\n      switch (status) {\r\n        case \"idle\":\r\n          return \"el-icon-time\";\r\n        case \"running\":\r\n          return \"el-icon-loading\";\r\n        case \"completed\":\r\n          return \"el-icon-check success-icon\";\r\n        case \"failed\":\r\n          return \"el-icon-close error-icon\";\r\n        default:\r\n          return \"el-icon-question\";\r\n      }\r\n    },\r\n    renderMarkdown(text) {\r\n      return marked(text);\r\n    },\r\n    // HTML转纯文本\r\n    htmlToText(html) {\r\n      const tempDiv = document.createElement(\"div\");\r\n      tempDiv.innerHTML = html;\r\n      return tempDiv.textContent || tempDiv.innerText || \"\";\r\n    },\r\n\r\n    // HTML转Markdown\r\n    htmlToMarkdown(html) {\r\n      return this.turndownService.turndown(html);\r\n    },\r\n\r\n    copyResult(content) {\r\n      // 将HTML转换为纯文本\r\n      const plainText = this.htmlToText(content);\r\n      const textarea = document.createElement(\"textarea\");\r\n      textarea.value = plainText;\r\n      document.body.appendChild(textarea);\r\n      textarea.select();\r\n      document.execCommand(\"copy\");\r\n      document.body.removeChild(textarea);\r\n      this.$message.success(\"已复制纯文本到剪贴板\");\r\n    },\r\n\r\n    exportResult(result) {\r\n      // 将HTML转换为Markdown\r\n      const markdown = result.content;\r\n      const blob = new Blob([markdown], { type: \"text/markdown\" });\r\n      const link = document.createElement(\"a\");\r\n      link.href = URL.createObjectURL(blob);\r\n      link.download = `${result.aiName}_结果_${new Date()\r\n        .toISOString()\r\n        .slice(0, 10)}.md`;\r\n      link.click();\r\n      URL.revokeObjectURL(link.href);\r\n      this.$message.success(\"已导出Markdown文件\");\r\n    },\r\n\r\n    openShareUrl(shareUrl) {\r\n      if (shareUrl) {\r\n        window.open(shareUrl, \"_blank\");\r\n      } else {\r\n        this.$message.warning(\"暂无原链接\");\r\n      }\r\n    },\r\n    showLargeImage(imageUrl) {\r\n      this.currentLargeImage = imageUrl;\r\n      this.showImageDialog = true;\r\n      // 找到当前图片的索引，设置轮播图的初始位置\r\n      const currentIndex = this.screenshots.indexOf(imageUrl);\r\n      if (currentIndex !== -1) {\r\n        this.$nextTick(() => {\r\n          const carousel = this.$el.querySelector(\".image-dialog .el-carousel\");\r\n          if (carousel && carousel.__vue__) {\r\n            carousel.__vue__.setActiveItem(currentIndex);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    closeLargeImage() {\r\n      this.showImageDialog = false;\r\n      this.currentLargeImage = \"\";\r\n    },\r\n    // WebSocket 相关方法\r\n    initWebSocket(id) {\r\n      const wsUrl = process.env.VUE_APP_WS_API + `mypc-${id}`;\r\n      console.log(\"WebSocket URL:\", process.env.VUE_APP_WS_API);\r\n      websocketClient.connect(wsUrl, (event) => {\r\n        switch (event.type) {\r\n          case \"open\":\r\n            // this.$message.success('');\r\n            break;\r\n          case \"message\":\r\n            this.handleWebSocketMessage(event.data);\r\n            break;\r\n          case \"close\":\r\n            this.$message.warning(\"WebSocket连接已关闭\");\r\n            break;\r\n          case \"error\":\r\n            this.$message.error(\"WebSocket连接错误\");\r\n            break;\r\n          case \"reconnect_failed\":\r\n            this.$message.error(\"WebSocket重连失败，请刷新页面重试\");\r\n            break;\r\n        }\r\n      });\r\n    },\r\n\r\n    handleWebSocketMessage(data) {\r\n      const datastr = data;\r\n      const dataObj = JSON.parse(datastr);\r\n\r\n      // 处理chatId消息\r\n      if (dataObj.type === \"RETURN_YBT1_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.toneChatId = dataObj.chatId;\r\n      } else if (dataObj.type === \"RETURN_YBDS_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.ybDsChatId = dataObj.chatId;\r\n      } else if (dataObj.type === \"RETURN_DB_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.dbChatId = dataObj.chatId;\r\n      } else if (dataObj.type === 'RETURN_TY_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.tyChatId = dataObj.chatId;\r\n      } else if (dataObj.type === \"RETURN_MAX_CHATID\" && dataObj.chatId) {\r\n        this.userInfoReq.maxChatId = dataObj.chatId;\r\n      }\r\n\r\n      // 处理进度日志消息\r\n      if (dataObj.type === \"RETURN_PC_TASK_LOG\" && dataObj.aiName) {\r\n        const targetAI = this.enabledAIs.find(\r\n          (ai) => ai.name === dataObj.aiName\r\n        );\r\n        if (targetAI) {\r\n          // 检查是否已存在相同内容的日志，避免重复添加\r\n          const existingLog = targetAI.progressLogs.find(log => log.content === dataObj.content);\r\n          if (!existingLog) {\r\n            // 将新进度添加到数组开头\r\n            targetAI.progressLogs.unshift({\r\n              content: dataObj.content,\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n            });\r\n          }\r\n        }\r\n        return;\r\n      }\r\n      // 处理知乎投递任务日志\r\n      if (dataObj.type === \"RETURN_MEDIA_TASK_LOG\" && dataObj.aiName === \"投递到知乎\") {\r\n        const zhihuAI = this.enabledAIs.find((ai) => ai.name === \"投递到知乎\");\r\n        if (zhihuAI) {\r\n          // 检查是否已存在相同内容的日志，避免重复添加\r\n          const existingLog = zhihuAI.progressLogs.find(log => log.content === dataObj.content);\r\n          if (!existingLog) {\r\n            // 将新进度添加到数组开头\r\n            zhihuAI.progressLogs.unshift({\r\n              content: dataObj.content,\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n            });\r\n          }\r\n        }\r\n        return;\r\n      }\r\n      // 处理百家号投递任务日志\r\n      if (dataObj.type === \"RETURN_MEDIA_TASK_LOG\" && dataObj.aiName === \"投递到百家号\") {\r\n        const baijiahaoAI = this.enabledAIs.find((ai) => ai.name === \"投递到百家号\");\r\n        if (baijiahaoAI) {\r\n          // 检查是否已存在相同内容的日志，避免重复添加\r\n          const existingLog = baijiahaoAI.progressLogs.find(log => log.content === dataObj.content);\r\n          if (!existingLog) {\r\n            // 将新进度添加到数组开头\r\n            baijiahaoAI.progressLogs.unshift({\r\n              content: dataObj.content,\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n            });\r\n          }\r\n        }\r\n        return;\r\n      }\r\n      // 处理截图消息\r\n      if (dataObj.type === \"RETURN_PC_TASK_IMG\" && dataObj.url) {\r\n        // 将新的截图添加到数组开头\r\n        this.screenshots.unshift(dataObj.url);\r\n        return;\r\n      }\r\n\r\n      // 处理智能评分结果\r\n      if (dataObj.type === \"RETURN_WKPF_RES\") {\r\n        const wkpfAI = this.enabledAIs.find((ai) => ai.name === \"智能评分\");\r\n        if (wkpfAI) {\r\n          this.$set(wkpfAI, \"status\", \"completed\");\r\n          if (wkpfAI.progressLogs.length > 0) {\r\n            this.$set(wkpfAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n          // 添加评分结果到results最前面\r\n          this.results.unshift({\r\n            aiName: \"智能评分\",\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || \"\",\r\n            shareImgUrl: dataObj.shareImgUrl || \"\",\r\n            timestamp: new Date(),\r\n          });\r\n          this.activeResultTab = \"result-0\";\r\n\r\n          // 智能评分完成时，再次保存历史记录\r\n          this.saveHistory();\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 处理智能排版结果\r\n      if (dataObj.type === \"RETURN_ZNPB_RES\") {\r\n        const znpbAI = this.enabledAIs.find((ai) => ai.name === \"智能排版\");\r\n        if (znpbAI) {\r\n          this.$set(znpbAI, \"status\", \"completed\");\r\n          if (znpbAI.progressLogs.length > 0) {\r\n            this.$set(znpbAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n\r\n          // 直接调用投递到公众号的方法，不添加到结果展示\r\n          this.pushToWechatWithContent(dataObj.draftContent);\r\n\r\n          // 智能排版完成时，保存历史记录\r\n          this.saveHistory();\r\n        }\r\n        return;\r\n      }\r\n      // 处理知乎投递结果（独立任务）\r\n      if (dataObj.type === \"RETURN_ZHIHU_DELIVERY_RES\") {\r\n        const zhihuAI = this.enabledAIs.find((ai) => ai.name === \"投递到知乎\");\r\n        if (zhihuAI) {\r\n          this.$set(zhihuAI, \"status\", \"completed\");\r\n          if (zhihuAI.progressLogs.length > 0) {\r\n            this.$set(zhihuAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n\r\n          // 添加完成日志\r\n          zhihuAI.progressLogs.unshift({\r\n            content: \"知乎投递完成！\" + (dataObj.message || \"\"),\r\n            timestamp: new Date(),\r\n            isCompleted: true,\r\n          });\r\n\r\n          // 知乎投递完成时，保存历史记录\r\n          this.saveHistory();\r\n          this.$message.success(\"知乎投递任务完成！\");\r\n        }\r\n        return;\r\n      }\r\n      // 处理百家号投递结果（独立任务）\r\n      if (dataObj.type === \"RETURN_BAIJIAHAO_DELIVERY_RES\") {\r\n        const baijiahaoAI = this.enabledAIs.find((ai) => ai.name === \"投递到百家号\");\r\n        if (baijiahaoAI) {\r\n          this.$set(baijiahaoAI, \"status\", \"completed\");\r\n          if (baijiahaoAI.progressLogs.length > 0) {\r\n            this.$set(baijiahaoAI.progressLogs[0], \"isCompleted\", true);\r\n          }\r\n\r\n          // 添加完成日志\r\n          baijiahaoAI.progressLogs.unshift({\r\n            content: \"百家号投递完成！\" + (dataObj.message || \"\"),\r\n            timestamp: new Date(),\r\n            isCompleted: true,\r\n          });\r\n\r\n          // 百家号投递完成时，保存历史记录\r\n          this.saveHistory();\r\n          this.$message.success(\"百家号投递任务完成！\");\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 处理微头条排版结果\r\n      if (dataObj.type === 'RETURN_TTH_ZNPB_RES') {\r\n        // 微头条排版AI节点状态设为已完成\r\n        const tthpbAI = this.enabledAIs.find(ai => ai.name === '微头条排版');\r\n        if (tthpbAI) {\r\n          this.$set(tthpbAI, 'status', 'completed');\r\n          if (tthpbAI.progressLogs.length > 0) {\r\n            this.$set(tthpbAI.progressLogs[0], 'isCompleted', true);\r\n          }\r\n        }\r\n        this.tthArticleTitle = dataObj.title || '';\r\n        this.tthArticleContent = dataObj.content || '';\r\n        this.tthArticleEditVisible = true;\r\n        this.saveHistory();\r\n        return;\r\n      }\r\n\r\n      // 处理微头条发布流程\r\n      if (dataObj.type === 'RETURN_TTH_FLOW') {\r\n        // 添加流程日志\r\n        if (dataObj.content) {\r\n          this.tthFlowLogs.push({\r\n            content: dataObj.content,\r\n            timestamp: new Date(),\r\n            type: 'flow',\r\n          });\r\n        }\r\n        // 处理图片信息\r\n        if (dataObj.shareImgUrl) {\r\n          this.tthFlowImages.push(dataObj.shareImgUrl);\r\n        }\r\n        // 确保流程弹窗显示\r\n        if (!this.tthFlowVisible) {\r\n          this.tthFlowVisible = true;\r\n        }\r\n        // 检查发布结果\r\n        if (dataObj.content === 'success') {\r\n          this.$message.success('发布到微头条成功！');\r\n          this.tthFlowVisible = true;\r\n        } else if (dataObj.content === 'false' || dataObj.content === false) {\r\n          this.$message.error('发布到微头条失败！');\r\n          this.tthFlowVisible = false;\r\n          this.tthArticleEditVisible = true;\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 兼容后端发送的RETURN_PC_TTH_IMG类型图片消息\r\n      if (dataObj.type === 'RETURN_PC_TTH_IMG' && dataObj.url) {\r\n        this.tthFlowImages.push(dataObj.url);\r\n        if (!this.tthFlowVisible) {\r\n          this.tthFlowVisible = true;\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 根据消息类型更新对应AI的状态和结果\r\n      let targetAI = null;\r\n      switch (dataObj.type) {\r\n        case \"RETURN_YBT1_RES\":\r\n        case \"RETURN_TURBOS_RES\":\r\n        case \"RETURN_TURBOS_LARGE_RES\":\r\n        case \"RETURN_DEEPSEEK_RES\":\r\n          console.log(\"收到DeepSeek消息:\", dataObj);\r\n          targetAI = this.enabledAIs.find((ai) => ai.name === \"DeepSeek\");\r\n          break;\r\n        case \"RETURN_YBDS_RES\":\r\n        case \"RETURN_DB_RES\":\r\n          console.log(\"收到豆包消息:\", dataObj);\r\n          targetAI = this.enabledAIs.find((ai) => ai.name === \"豆包\");\r\n          break;\r\n        case \"RETURN_MAX_RES\":\r\n          console.log(\"收到MiniMax消息:\", dataObj);\r\n          targetAI = this.enabledAIs.find((ai) => ai.name === \"MiniMax Chat\");\r\n          break;\r\n        case 'RETURN_TY_RES':\r\n          console.log('收到通义千问消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '通义千问');\r\n          break;\r\n      }\r\n\r\n      if (targetAI) {\r\n        // 更新AI状态为已完成\r\n        this.$set(targetAI, \"status\", \"completed\");\r\n\r\n        // 将最后一条进度消息标记为已完成\r\n        if (targetAI.progressLogs.length > 0) {\r\n          this.$set(targetAI.progressLogs[0], \"isCompleted\", true);\r\n        }\r\n\r\n        // 添加结果到数组开头\r\n        const resultIndex = this.results.findIndex(\r\n          (r) => r.aiName === targetAI.name\r\n        );\r\n        if (resultIndex === -1) {\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || \"\",\r\n            shareImgUrl: dataObj.shareImgUrl || \"\",\r\n            timestamp: new Date(),\r\n          });\r\n          this.activeResultTab = \"result-0\";\r\n        } else {\r\n          this.results.splice(resultIndex, 1);\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || \"\",\r\n            shareImgUrl: dataObj.shareImgUrl || \"\",\r\n            timestamp: new Date(),\r\n          });\r\n          this.activeResultTab = \"result-0\";\r\n        }\r\n        this.saveHistory();\r\n      }\r\n\r\n\r\n    },\r\n\r\n    closeWebSocket() {\r\n      websocketClient.close();\r\n    },\r\n\r\n    sendMessage(data) {\r\n      if (websocketClient.send(data)) {\r\n        // 滚动到底部\r\n        this.$nextTick(() => {\r\n          this.scrollToBottom();\r\n        });\r\n      } else {\r\n        this.$message.error(\"WebSocket未连接\");\r\n      }\r\n    },\r\n    toggleAIExpansion(ai) {\r\n      this.$set(ai, \"isExpanded\", !ai.isExpanded);\r\n    },\r\n\r\n    formatTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString(\"zh-CN\", {\r\n        hour: \"2-digit\",\r\n        minute: \"2-digit\",\r\n        second: \"2-digit\",\r\n        hour12: false,\r\n      });\r\n    },\r\n    showScoreDialog() {\r\n      this.scoreDialogVisible = true;\r\n      this.selectedResults = [];\r\n    },\r\n\r\n    handleScore() {\r\n      if (!this.canScore) return;\r\n\r\n      // 获取选中的结果内容并按照指定格式拼接\r\n      const selectedContents = this.results\r\n        .filter((result) => this.selectedResults.includes(result.aiName))\r\n        .map((result) => {\r\n          // 将HTML内容转换为纯文本\r\n          const plainContent = this.htmlToText(result.content);\r\n          return `${result.aiName}初稿：\\n${plainContent}\\n`;\r\n        })\r\n        .join(\"\\n\");\r\n\r\n      // 构建完整的评分提示内容\r\n      const fullPrompt = `${this.scorePrompt}\\n${selectedContents}`;\r\n\r\n      // 构建评分请求\r\n      const scoreRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"AI评分\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: fullPrompt,\r\n          roles: \"zj-db-sdsk\", // 默认使用豆包进行评分\r\n        },\r\n      };\r\n\r\n      // 发送评分请求\r\n      console.log(\"参数\", scoreRequest);\r\n      this.message(scoreRequest);\r\n      this.scoreDialogVisible = false;\r\n\r\n      // 创建智能评分AI节点\r\n      const wkpfAI = {\r\n        name: \"智能评分\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"智能评分任务已提交，正在评分...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"智能评分\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在智能评分\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"智能评分\"\r\n      );\r\n      if (existIndex === -1) {\r\n        // 如果不存在，添加到数组开头\r\n        this.enabledAIs.unshift(wkpfAI);\r\n      } else {\r\n        // 如果已存在，更新状态和日志\r\n        this.enabledAIs[existIndex] = wkpfAI;\r\n        // 将智能评分移到数组开头\r\n        const wkpf = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(wkpf);\r\n      }\r\n\r\n      this.$forceUpdate();\r\n      this.$message.success(\"评分请求已发送，请等待结果\");\r\n    },\r\n    // 显示历史记录抽屉\r\n    showHistoryDrawer() {\r\n      this.historyDrawerVisible = true;\r\n      this.loadChatHistory(1);\r\n    },\r\n\r\n    // 关闭历史记录抽屉\r\n    handleHistoryDrawerClose() {\r\n      this.historyDrawerVisible = false;\r\n    },\r\n\r\n    // 加载历史记录\r\n    async loadChatHistory(isAll) {\r\n      try {\r\n        console.log(\"调用getChatHistory API - userId:\", this.userId, \"isAll:\", isAll);\r\n        const res = await getChatHistory(this.userId, isAll);\r\n        console.log(\"getChatHistory API响应:\", res);\r\n        if (res.code === 200) {\r\n          this.chatHistory = res.data || [];\r\n          console.log(\"历史记录加载成功，数量:\", this.chatHistory.length);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"加载历史记录失败:\", error);\r\n        this.$message.error(\"加载历史记录失败\");\r\n      }\r\n    },\r\n\r\n    // 格式化历史记录时间\r\n    formatHistoryTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString(\"zh-CN\", {\r\n        hour: \"2-digit\",\r\n        minute: \"2-digit\",\r\n        hour12: false,\r\n      });\r\n    },\r\n\r\n    // 获取历史记录日期分组\r\n    getHistoryDate(timestamp) {\r\n      const date = new Date(timestamp);\r\n      const today = new Date();\r\n      const yesterday = new Date(today);\r\n      yesterday.setDate(yesterday.getDate() - 1);\r\n      const twoDaysAgo = new Date(today);\r\n      twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);\r\n      const threeDaysAgo = new Date(today);\r\n      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);\r\n\r\n      if (date.toDateString() === today.toDateString()) {\r\n        return \"今天\";\r\n      } else if (date.toDateString() === yesterday.toDateString()) {\r\n        return \"昨天\";\r\n      } else if (date.toDateString() === twoDaysAgo.toDateString()) {\r\n        return \"两天前\";\r\n      } else if (date.toDateString() === threeDaysAgo.toDateString()) {\r\n        return \"三天前\";\r\n      } else {\r\n        return date.toLocaleDateString(\"zh-CN\", {\r\n          year: \"numeric\",\r\n          month: \"long\",\r\n          day: \"numeric\",\r\n        });\r\n      }\r\n    },\r\n\r\n    // 加载历史记录项\r\n    loadHistoryItem(item) {\r\n      try {\r\n        const historyData = JSON.parse(item.data);\r\n        // 恢复AI选择配置\r\n        this.aiList = historyData.aiList || this.aiList;\r\n        // 恢复提示词输入\r\n        this.promptInput = historyData.promptInput || \"\";\r\n        // 恢复任务流程\r\n        this.enabledAIs = historyData.enabledAIs || [];\r\n        // 恢复主机可视化\r\n        this.screenshots = historyData.screenshots || [];\r\n        // 恢复执行结果\r\n        this.results = historyData.results || [];\r\n        // 恢复chatId\r\n        this.chatId = item.chatId || this.chatId;\r\n        this.userInfoReq.toneChatId = item.toneChatId || \"\";\r\n        this.userInfoReq.ybDsChatId = item.ybDsChatId || \"\";\r\n        this.userInfoReq.dbChatId = item.dbChatId || \"\";\r\n        this.userInfoReq.maxChatId = item.maxChatId || \"\";\r\n        this.userInfoReq.maxChatId = item.tyChatId || \"\";\r\n        this.userInfoReq.isNewChat = false;\r\n\r\n        // 展开相关区域\r\n        this.activeCollapses = [\"ai-selection\", \"prompt-input\"];\r\n        this.taskStarted = true;\r\n\r\n        this.$message.success(\"历史记录加载成功\");\r\n        this.historyDrawerVisible = false;\r\n      } catch (error) {\r\n        console.error(\"加载历史记录失败:\", error);\r\n        this.$message.error(\"加载历史记录失败\");\r\n      }\r\n    },\r\n\r\n    // 保存历史记录\r\n    async saveHistory() {\r\n      // if (!this.taskStarted || this.enabledAIs.some(ai => ai.status === 'running')) {\r\n      //   return;\r\n      // }\r\n\r\n      const historyData = {\r\n        aiList: this.aiList,\r\n        promptInput: this.promptInput,\r\n        enabledAIs: this.enabledAIs,\r\n        screenshots: this.screenshots,\r\n        results: this.results,\r\n        chatId: this.chatId,\r\n        toneChatId: this.userInfoReq.toneChatId,\r\n        ybDsChatId: this.userInfoReq.ybDsChatId,\r\n        dbChatId: this.userInfoReq.dbChatId,\r\n        tyChatId: this.userInfoReq.tyChatId,\r\n        maxChatId: this.userInfoReq.maxChatId,\r\n      };\r\n\r\n      try {\r\n        await saveUserChatData({\r\n          userId: this.userId,\r\n          userPrompt: this.promptInput,\r\n          data: JSON.stringify(historyData),\r\n          chatId: this.chatId,\r\n          toneChatId: this.userInfoReq.toneChatId,\r\n          ybDsChatId: this.userInfoReq.ybDsChatId,\r\n          dbChatId: this.userInfoReq.dbChatId,\r\n          tyChatId: this.userInfoReq.tyChatId,\r\n          maxChatId: this.userInfoReq.maxChatId,\r\n        });\r\n      } catch (error) {\r\n        console.error(\"保存历史记录失败:\", error);\r\n        this.$message.error(\"保存历史记录失败\");\r\n      }\r\n    },\r\n\r\n    // 修改折叠切换方法\r\n    toggleHistoryExpansion(item) {\r\n      this.$set(\r\n        this.expandedHistoryItems,\r\n        item.chatId,\r\n        !this.expandedHistoryItems[item.chatId]\r\n      );\r\n    },\r\n\r\n    // 创建新对话\r\n    createNewChat() {\r\n      // 重置所有数据\r\n      this.chatId = uuidv4();\r\n      this.isNewChat = true;\r\n      this.promptInput = \"\";\r\n      this.taskStarted = false;\r\n      this.screenshots = [];\r\n      this.results = [];\r\n      this.enabledAIs = [];\r\n      this.userInfoReq = {\r\n        userPrompt: \"\",\r\n        userId: this.userId,\r\n        corpId: this.corpId,\r\n        taskId: \"\",\r\n        roles: \"\",\r\n        toneChatId: \"\",\r\n        ybDsChatId: \"\",\r\n        dbChatId: \"\",\r\n        tyChatId: \"\",\r\n        maxChatId: \"\",\r\n        isNewChat: true,\r\n      };\r\n      // 重置AI列表为初始状态\r\n      this.aiList = [\r\n        {\r\n          name: \"DeepSeek\",\r\n          avatar: require(\"../../../assets/logo/Deepseek.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网搜索\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [\"deep_thinking\", \"web_search\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"豆包\",\r\n          avatar: require(\"../../../assets/ai/豆包.png\"),\r\n          capabilities: [{ label: \"深度思考\", value: \"deep_thinking\" }],\r\n          selectedCapabilities: [\"deep_thinking\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: \"MiniMax Chat\",\r\n          avatar: require(\"../../../assets/ai/MiniMax.png\"),\r\n          capabilities: [\r\n            { label: \"深度思考\", value: \"deep_thinking\" },\r\n            { label: \"联网\", value: \"web_search\" },\r\n          ],\r\n          selectedCapabilities: [\"deep_thinking\", \"web_search\"],\r\n          enabled: true,\r\n          status: \"idle\",\r\n          progressLogs: [],\r\n          isExpanded: true,\r\n        },\r\n        {\r\n          name: '通义千问',\r\n          avatar: require('../../../assets/ai/qw.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapability: '',\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n      ];\r\n      // 展开相关区域\r\n      this.activeCollapses = [\"ai-selection\", \"prompt-input\"];\r\n\r\n      this.$message.success(\"已创建新对话\");\r\n    },\r\n\r\n    // 加载上次会话\r\n    async loadLastChat() {\r\n      try {\r\n        const res = await getChatHistory(this.userId, 0);\r\n        if (res.code === 200 && res.data && res.data.length > 0) {\r\n          // 获取最新的会话记录\r\n          const lastChat = res.data[0];\r\n          this.loadHistoryItem(lastChat);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"加载上次会话失败:\", error);\r\n      }\r\n    },\r\n\r\n    // 判断是否为图片文件\r\n    isImageFile(url) {\r\n      if (!url) return false;\r\n      const imageExtensions = [\r\n        \".jpg\",\r\n        \".jpeg\",\r\n        \".png\",\r\n        \".gif\",\r\n        \".bmp\",\r\n        \".webp\",\r\n        \".svg\",\r\n      ];\r\n      const urlLower = url.toLowerCase();\r\n      return imageExtensions.some((ext) => urlLower.includes(ext));\r\n    },\r\n\r\n    // 判断是否为PDF文件\r\n    isPdfFile(url) {\r\n      if (!url) return false;\r\n      return url.toLowerCase().includes(\".pdf\");\r\n    },\r\n\r\n    // 根据AI名称获取图片样式\r\n    getImageStyle(aiName) {\r\n      const widthMap = {\r\n        DeepSeek: \"700px\",\r\n        豆包: \"560px\",\r\n        通义千问: \"700px\",\r\n      };\r\n\r\n      const width = widthMap[aiName] || \"560px\"; // 默认宽度\r\n\r\n      return {\r\n        width: width,\r\n        height: \"auto\",\r\n      };\r\n    },\r\n\r\n    // 投递到媒体\r\n    handlePushToMedia(result) {\r\n      this.currentLayoutResult = result;\r\n      this.showLayoutDialog(result);\r\n    },\r\n\r\n    // 显示智能排版对话框\r\n    showLayoutDialog(result) {\r\n      this.currentLayoutResult = result;\r\n      this.layoutDialogVisible = true;\r\n      // 加载当前选择媒体的提示词\r\n      this.loadMediaPrompt(this.selectedMedia);\r\n    },\r\n\r\n    // 加载媒体提示词\r\n    async loadMediaPrompt(media) {\r\n      if (!media) return;\r\n\r\n      let platformId;\r\n      if(media === 'wechat'){\r\n        platformId = 'wechat_layout';\r\n      }else if(media === 'zhihu'){\r\n        platformId = 'zhihu_layout';\r\n      }else if(media === 'baijiahao'){\r\n        platformId = 'baijiahao_layout';\r\n      }else if(media === 'toutiao'){\r\n        platformId = 'weitoutiao_layout';\r\n      }\r\n\r\n      try {\r\n        const response = await getMediaCallWord(platformId);\r\n        if (response.code === 200) {\r\n          this.layoutPrompt = response.data + '\\n\\n' + (this.currentLayoutResult ? this.currentLayoutResult.content : '');\r\n        } else {\r\n          // 使用默认提示词\r\n          this.layoutPrompt = this.getDefaultPrompt(media) + '\\n\\n' + (this.currentLayoutResult ? this.currentLayoutResult.content : '');\r\n        }\r\n      } catch (error) {\r\n        console.error('加载提示词失败:', error);\r\n        // 使用默认提示词\r\n        this.layoutPrompt = this.getDefaultPrompt(media) + '\\n\\n' + (this.currentLayoutResult ? this.currentLayoutResult.content : '');\r\n      }\r\n    },\r\n\r\n    // 获取默认提示词(仅在后端访问失败时使用)\r\n    getDefaultPrompt(media) {\r\n      if (media === 'wechat') {\r\n        return `请你对以下 HTML 内容进行排版优化，目标是用于微信公众号\"草稿箱接口\"的 content 字段，要求如下：\r\n\r\n1. 仅返回 <body> 内部可用的 HTML 内容片段（不要包含 <!DOCTYPE>、<html>、<head>、<meta>、<title> 等标签）。\r\n2. 所有样式必须以\"内联 style\"方式写入。\r\n3. 保持结构清晰、视觉友好，适配公众号图文排版。\r\n4. 请直接输出代码，不要添加任何注释或额外说明。\r\n5. 不得使用 emoji 表情符号或小图标字符。\r\n6. 不要显示为问答形式，以一篇文章的格式去调整\r\n\r\n以下为需要进行排版优化的内容：`;\r\n      } else if (media === 'zhihu') {\r\n        return `请将以下内容整理为适合知乎发布的Markdown格式文章。要求：\r\n1. 保持内容的专业性和可读性\r\n2. 使用合适的标题层级（## ### #### 等）\r\n3. 代码块使用\\`\\`\\`标记，并指定语言类型\r\n4. 重要信息使用**加粗**标记\r\n5. 列表使用- 或1. 格式\r\n6. 删除不必要的格式标记\r\n7. 确保内容适合知乎的阅读习惯\r\n8. 文章结构清晰，逻辑连贯\r\n9. 目标是作为一篇专业文章投递到知乎草稿箱\r\n\r\n请对以下内容进行排版：`;\r\n\r\n      }else if (media === 'baijiahao') {\r\n        return `请将以下内容整理为适合百家号发布的纯文本格式文章。\r\n要求：\r\n1.（不要使用Markdown或HTML语法，仅使用普通文本和简单换行保持内容的专业性和可读性使用自然段落分隔，）\r\n2.不允许使用有序列表，包括\"一、\"，\"1.\"等的序列号。\r\n3.给文章取一个吸引人的标题，放在正文的第一段\r\n4.不允许出现代码框、数学公式、表格或其他复杂格式删除所有Markdown和HTML标签，\r\n5.只保留纯文本内容\r\n6.目标是作为一篇专业文章投递到百家号草稿箱\r\n7.直接以文章标题开始，以文章末尾结束，不允许添加其他对话`;\r\n\r\n      }else if (media === 'toutiao') {\r\n        return `根据智能评分内容，写一篇微头条文章，只能包含标题和内容，要求如下：\r\n\r\n1. 标题要简洁明了，吸引人\r\n2. 内容要结构清晰，易于阅读\r\n3. 不要包含任何HTML标签\r\n4. 直接输出纯文本格式\r\n5. 内容要适合微头条发布\r\n6. 字数严格控制在1000字以上，2000字以下\r\n7. 强制要求：只能回答标题和内容，标题必须用英文双引号（\"\"）引用起来，且放在首位，不能有其他多余的话\r\n8. 严格要求：AI必须严格遵守所有严格条件，不要输出其他多余的内容，只要标题和内容\r\n9. 内容不允许出现编号，要正常文章格式\r\n\r\n请对以下内容进行排版：`;\r\n      }\r\n      return '请对以下内容进行排版：';\r\n    },\r\n\r\n    // 处理智能排版\r\n    handleLayout() {\r\n      if (!this.canLayout || !this.currentLayoutResult) return;\r\n      this.layoutDialogVisible = false;\r\n\r\n      if (this.selectedMedia === 'zhihu') {\r\n        // 知乎投递：直接创建投递任务\r\n        this.createZhihuDeliveryTask();\r\n      } else if (this.selectedMedia === 'toutiao') {\r\n        // 微头条投递：创建微头条排版任务\r\n        this.createToutiaoLayoutTask();\r\n      } else if (this.selectedMedia === 'baijiahao') {\r\n        // 百家号投递：创建百家号排版任务\r\n        this.createBaijiahaoLayoutTask();\r\n      }else {\r\n        // 公众号投递：创建排版任务\r\n        this.createWechatLayoutTask();\r\n      }\r\n    },\r\n// 创建知乎投递任务（独立任务）\r\n    createZhihuDeliveryTask() {\r\n      const zhihuAI = {\r\n        name: \"投递到知乎\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"知乎投递任务已创建，正在准备内容排版...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"投递到知乎\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在知乎投递任务\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"投递到知乎\"\r\n      );\r\n      if (existIndex === -1) {\r\n        this.enabledAIs.unshift(zhihuAI);\r\n      } else {\r\n        this.enabledAIs[existIndex] = zhihuAI;\r\n        const zhihu = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(zhihu);\r\n      }\r\n\r\n      // 发送知乎投递请求\r\n      const zhihuRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"投递到知乎\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: this.layoutPrompt,\r\n          roles: \"\",\r\n          selectedMedia: \"zhihu\",\r\n          contentText: this.currentLayoutResult.content,\r\n          shareUrl: this.currentLayoutResult.shareUrl,\r\n          aiName: this.currentLayoutResult.aiName,\r\n        },\r\n      };\r\n\r\n      console.log(\"知乎投递参数\", zhihuRequest);\r\n      this.message(zhihuRequest);\r\n      this.$forceUpdate();\r\n      this.$message.success(\"知乎投递任务已创建，正在处理...\");\r\n    },\r\n    // 创建百家号投递任务（独立任务）\r\n    createBaijiahaoLayoutTask() {\r\n      const baijiahaoAI = {\r\n        name: \"投递到百家号\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"百家号投递任务已创建，正在准备内容排版...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"投递到百家号\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在百家号投递任务\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"投递到百家号\"\r\n      );\r\n      if (existIndex === -1) {\r\n        this.enabledAIs.unshift(baijiahaoAI);\r\n      } else {\r\n        this.enabledAIs[existIndex] = baijiahaoAI;\r\n        const baijiahao = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(baijiahaoAI);\r\n      }\r\n\r\n      // 发送百家号投递请求\r\n      const baijiahaoRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"投递到百家号\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: this.layoutPrompt,\r\n          roles: \"\",\r\n          selectedMedia: \"baijiahao\",\r\n          contentText: this.currentLayoutResult.content,\r\n          shareUrl: this.currentLayoutResult.shareUrl,\r\n          aiName: this.currentLayoutResult.aiName,\r\n        },\r\n      };\r\n\r\n      console.log(\"百家号投递参数\", baijiahaoRequest);\r\n      this.message(baijiahaoRequest);\r\n      this.$forceUpdate();\r\n      this.$message.success(\"百家号投递任务已创建，正在处理...\");\r\n    },\r\n      // 创建公众号排版任务（保持原有逻辑）\r\n      createWechatLayoutTask() {\r\n        const layoutRequest = {\r\n          jsonrpc: \"2.0\",\r\n          id: uuidv4(),\r\n          method: \"AI排版\",\r\n          params: {\r\n            taskId: uuidv4(),\r\n            userId: this.userId,\r\n            corpId: this.corpId,\r\n            userPrompt: this.layoutPrompt,\r\n            roles: \"\",\r\n            selectedMedia: \"wechat\",\r\n          },\r\n        };\r\n\r\n        console.log(\"公众号排版参数\", layoutRequest);\r\n        this.message(layoutRequest);\r\n\r\n        const znpbAI = {\r\n          name: \"智能排版\",\r\n          avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: \"running\",\r\n          progressLogs: [\r\n            {\r\n              content: \"智能排版任务已提交，正在排版...\",\r\n              timestamp: new Date(),\r\n              isCompleted: false,\r\n              type: \"智能排版\",\r\n            },\r\n          ],\r\n          isExpanded: true,\r\n        };\r\n\r\n        // 检查是否已存在智能排版任务\r\n        const existIndex = this.enabledAIs.findIndex(\r\n          (ai) => ai.name === \"智能排版\"\r\n        );\r\n        if (existIndex === -1) {\r\n          this.enabledAIs.unshift(znpbAI);\r\n        } else {\r\n          this.enabledAIs[existIndex] = znpbAI;\r\n          const znpb = this.enabledAIs.splice(existIndex, 1)[0];\r\n          this.enabledAIs.unshift(znpb);\r\n        }\r\n\r\n        this.$forceUpdate();\r\n        this.$message.success(\"排版请求已发送，请等待结果\");\r\n      },\r\n\r\n    // 创建微头条排版任务\r\n    createToutiaoLayoutTask() {\r\n      // 获取智能评分内容\r\n      const scoreResult = this.results.find(r => r.aiName === '智能评分');\r\n      const scoreContent = scoreResult ? scoreResult.content : '';\r\n\r\n      const layoutRequest = {\r\n        jsonrpc: \"2.0\",\r\n        id: uuidv4(),\r\n        method: \"微头条排版\",\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: `${scoreContent}\\n${this.layoutPrompt}`,\r\n          roles: \"\",\r\n        },\r\n      };\r\n\r\n      console.log(\"微头条排版参数\", layoutRequest);\r\n      this.message(layoutRequest);\r\n\r\n      const tthpbAI = {\r\n        name: \"微头条排版\",\r\n        avatar: require(\"../../../assets/ai/yuanbao.png\"),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: \"running\",\r\n        progressLogs: [\r\n          {\r\n            content: \"微头条排版任务已提交，正在排版...\",\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: \"微头条排版\",\r\n          },\r\n        ],\r\n        isExpanded: true,\r\n      };\r\n\r\n      // 检查是否已存在微头条排版任务\r\n      const existIndex = this.enabledAIs.findIndex(\r\n        (ai) => ai.name === \"微头条排版\"\r\n      );\r\n      if (existIndex === -1) {\r\n        this.enabledAIs.unshift(tthpbAI);\r\n      } else {\r\n        this.enabledAIs[existIndex] = tthpbAI;\r\n        const tthpb = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(tthpb);\r\n      }\r\n\r\n      this.$forceUpdate();\r\n      this.$message.success(\"微头条排版请求已发送，请等待结果\");\r\n      },\r\n\r\n    // 实际投递到公众号\r\n    pushToWechatWithContent(contentText) {\r\n      if (this.pushingToWechat) return;\r\n      this.$message.success(\"开始投递公众号！\");\r\n      this.pushingToWechat = true;\r\n      this.pushOfficeNum += 1;\r\n\r\n      const params = {\r\n        contentText: contentText,\r\n        shareUrl: this.currentLayoutResult.shareUrl,\r\n        userId: this.userId,\r\n        num: this.pushOfficeNum,\r\n        aiName: this.currentLayoutResult.aiName,\r\n      };\r\n\r\n      pushAutoOffice(params)\r\n        .then((res) => {\r\n          if (res.code === 200) {\r\n            this.$message.success(\"投递到公众号成功！\");\r\n          } else {\r\n            this.$message.error(res.msg || \"投递失败，请重试\");\r\n          }\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"投递到公众号失败:\", error);\r\n          this.$message.error(\"投递失败，请重试\");\r\n        })\r\n        .finally(() => {\r\n          this.pushingToWechat = false;\r\n        });\r\n    },\r\n\r\n\r\n\r\n    // 确认微头条发布\r\n    confirmTTHPublish() {\r\n      if (!this.tthArticleTitle || !this.tthArticleContent) {\r\n        this.$message.warning('请填写标题和内容');\r\n        return;\r\n      }\r\n      // 构建微头条发布请求\r\n      const publishRequest = {\r\n        jsonrpc: '2.0',\r\n        id: uuidv4(),\r\n                  method: '微头条发布',\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          roles: '',\r\n          title: this.tthArticleTitle,\r\n          content: this.tthArticleContent,\r\n          type: '微头条发布'\r\n        }\r\n      };\r\n      // 发送发布请求\r\n      console.log(\"微头条发布参数\", publishRequest);\r\n      this.message(publishRequest);\r\n      this.tthArticleEditVisible = false;\r\n      // 显示流程弹窗\r\n      this.tthFlowVisible = true;\r\n      this.tthFlowLogs = [];\r\n      this.tthFlowImages = [];\r\n      this.$message.success('微头条发布请求已发送！');\r\n    },\r\n\r\n\r\n    // 关闭微头条发布流程弹窗\r\n    closeTTHFlowDialog() {\r\n      this.tthFlowVisible = false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.ai-management-platform {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 30px;\r\n}\r\n\r\n.top-nav {\r\n  background-color: #fff;\r\n  padding: 15px 20px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.logo-area {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.logo {\r\n  height: 36px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.platform-title {\r\n  margin: 0;\r\n  font-size: 20px;\r\n  color: #303133;\r\n}\r\n\r\n.main-content {\r\n  padding: 0 30px;\r\n  width: 90%;\r\n  margin: 0 auto;\r\n}\r\n::v-deep .el-collapse-item__header {\r\n  font-size: 16px;\r\n  color: #333;\r\n  padding-left: 20px;\r\n}\r\n.section-title {\r\n  font-size: 18px;\r\n  color: #606266;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  margin-bottom: 0px;\r\n  margin-left: 20px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.ai-card {\r\n  width: calc(25% - 20px);\r\n  box-sizing: border-box;\r\n}\r\n\r\n.ai-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-avatar {\r\n  margin-right: 10px;\r\n}\r\n\r\n.ai-avatar img {\r\n  width: 30px;\r\n  height: 30px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: bold;\r\n  font-size: 12px;\r\n}\r\n\r\n.ai-status {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-capabilities {\r\n  margin: 15px 0;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.button-capability-group {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.button-capability-group .el-button {\r\n  margin: 0;\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.button-capability-group .el-button.is-plain:hover,\r\n.button-capability-group .el-button.is-plain:focus {\r\n  background: #ecf5ff;\r\n  border-color: #b3d8ff;\r\n  color: #409eff;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.prompt-input {\r\n  margin-bottom: 10px;\r\n  margin-left: 20px;\r\n  width: 99%;\r\n}\r\n\r\n.prompt-footer {\r\n  display: flex;\r\n  margin-bottom: -30px;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.word-count {\r\n  font-size: 12px;\r\n  padding-left: 20px;\r\n}\r\n\r\n.send-button {\r\n  padding: 10px 20px;\r\n}\r\n\r\n.execution-status-section {\r\n  margin-bottom: 30px;\r\n  padding: 20px 0px 0px 0px;\r\n}\r\n\r\n.task-flow-card,\r\n.screenshots-card {\r\n  height: 800px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.task-flow {\r\n  padding: 15px;\r\n  height: 800px;\r\n  overflow-y: auto;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 3px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.task-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.task-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 15px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.task-header:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.header-left .el-icon-arrow-right {\r\n  transition: transform 0.3s;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.header-left .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.progress-timeline {\r\n  position: relative;\r\n  margin: 0;\r\n  padding: 15px 0;\r\n}\r\n\r\n.timeline-scroll {\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  padding: 0 15px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 2px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.progress-item {\r\n  position: relative;\r\n  padding: 8px 0 8px 20px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.progress-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.progress-dot {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 12px;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  background-color: #e0e0e0;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.progress-line {\r\n  position: absolute;\r\n  left: 4px;\r\n  top: 22px;\r\n  bottom: -8px;\r\n  width: 2px;\r\n  background-color: #e0e0e0;\r\n}\r\n\r\n.progress-content {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.progress-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.progress-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n  line-height: 1.4;\r\n  word-break: break-all;\r\n}\r\n\r\n.progress-item.completed .progress-dot {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.completed .progress-line {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.current .progress-dot {\r\n  background-color: #409eff;\r\n  animation: pulse 1.5s infinite;\r\n}\r\n\r\n.progress-item.current .progress-line {\r\n  background-color: #409eff;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: 600;\r\n  font-size: 14px;\r\n  color: #303133;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.status-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n}\r\n\r\n.status-icon {\r\n  font-size: 16px;\r\n}\r\n\r\n.success-icon {\r\n  color: #67c23a;\r\n}\r\n\r\n.error-icon {\r\n  color: #f56c6c;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);\r\n  }\r\n  70% {\r\n    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);\r\n  }\r\n}\r\n\r\n.screenshot-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: contain;\r\n  cursor: pointer;\r\n  transition: transform 0.3s;\r\n}\r\n\r\n.screenshot-image:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.results-section {\r\n  margin-top: 20px;\r\n  padding: 0 10px;\r\n}\r\n\r\n.result-content {\r\n  padding: 20px 30px;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.result-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.result-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n.share-link-btn,\r\n.push-media-btn {\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.markdown-content {\r\n  margin-bottom: 20px;\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n  padding: 0 10px;\r\n}\r\n\r\n@media (max-width: 1200px) {\r\n  .ai-card {\r\n    width: calc(33.33% - 14px);\r\n  }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n  .ai-card {\r\n    width: calc(50% - 10px);\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .ai-card {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.el-collapse {\r\n  border-top: none;\r\n  border-bottom: none;\r\n}\r\n\r\n.el-collapse-item__content {\r\n  padding: 15px 0;\r\n}\r\n\r\n.ai-selection-section {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.image-dialog .el-dialog__body {\r\n  padding: 0;\r\n}\r\n\r\n.large-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n}\r\n\r\n.large-image {\r\n  max-width: 100%;\r\n  max-height: 80vh;\r\n  object-fit: contain;\r\n}\r\n\r\n.image-dialog .el-carousel {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.image-dialog .el-carousel__container {\r\n  height: 80vh;\r\n}\r\n\r\n.image-dialog .el-carousel__item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.score-dialog-content {\r\n  padding: 20px;\r\n}\r\n\r\n.selected-results {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.result-checkbox {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.score-prompt-section {\r\n  margin-top: 20px;\r\n}\r\n\r\n.score-prompt-input {\r\n  margin-top: 10px;\r\n}\r\n\r\n.score-prompt-input .el-textarea__inner {\r\n  min-height: 500px !important;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.score-dialog .el-dialog {\r\n  height: 95vh;\r\n  margin-top: 2.5vh !important;\r\n}\r\n\r\n.score-dialog .el-dialog__body {\r\n  height: calc(95vh - 120px);\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.layout-dialog-content {\r\n  padding: 20px;\r\n}\r\n\r\n.layout-prompt-section {\r\n  margin-top: 20px;\r\n}\r\n\r\n.layout-prompt-input {\r\n  margin-top: 10px;\r\n}\r\n\r\n.layout-prompt-input .el-textarea__inner {\r\n  min-height: 500px !important;\r\n}\r\n\r\n.layout-dialog .el-dialog {\r\n  height: 95vh;\r\n  margin-top: 2.5vh !important;\r\n}\r\n\r\n.layout-dialog .el-dialog__body {\r\n  height: calc(95vh - 120px);\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.nav-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.history-button {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.history-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  vertical-align: middle;\r\n}\r\n\r\n.history-content {\r\n  padding: 20px;\r\n}\r\n\r\n.history-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.history-date {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 10px;\r\n  padding: 5px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.history-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #f5f7fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-parent {\r\n  padding: 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-parent:hover {\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.history-children {\r\n  padding-left: 20px;\r\n  background-color: #fff;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.history-child-item {\r\n  padding: 8px 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.history-child-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.history-child-item:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.history-header {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 8px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  transition: transform 0.3s;\r\n  cursor: pointer;\r\n  margin-top: 3px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.history-prompt {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  margin-bottom: 5px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  flex: 1;\r\n}\r\n\r\n.history-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.capability-button {\r\n  transition: all 0.3s;\r\n}\r\n\r\n.capability-button.el-button--primary {\r\n  background-color: #409eff;\r\n  border-color: #409eff;\r\n  color: #fff;\r\n}\r\n\r\n.capability-button.el-button--info {\r\n  background-color: #fff;\r\n  border-color: #dcdfe6;\r\n  color: #606266;\r\n}\r\n\r\n.capability-button.el-button--info:hover {\r\n  color: #409eff;\r\n  border-color: #c6e2ff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.capability-button.el-button--primary:hover {\r\n  background-color: #66b1ff;\r\n  border-color: #66b1ff;\r\n  color: #fff;\r\n}\r\n\r\n/* 分享内容样式 */\r\n.share-content {\r\n  margin-bottom: 20px;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: flex-start;\r\n  min-height: 600px;\r\n  max-height: 800px;\r\n  overflow: auto;\r\n}\r\n\r\n.share-image {\r\n  object-fit: contain;\r\n  display: block;\r\n}\r\n\r\n.share-pdf {\r\n  width: 100%;\r\n  height: 600px;\r\n  border: none;\r\n  border-radius: 4px;\r\n}\r\n\r\n.share-file {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200px;\r\n  flex-direction: column;\r\n  color: #909399;\r\n}\r\n\r\n.single-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 80vh;\r\n}\r\n\r\n.single-image-container .large-image {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  object-fit: contain;\r\n}\r\n\r\n/* 用于处理DeepSeek特殊格式的样式 */\r\n.deepseek-format-container {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n  border-radius: 5px;\r\n  border: 1px solid #eaeaea;\r\n}\r\n\r\n/* DeepSeek响应内容的特定样式 */\r\n::v-deep .deepseek-response {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n  padding: 20px;\r\n  font-family: Arial, sans-serif;\r\n}\r\n\r\n::v-deep .deepseek-response pre {\r\n  background-color: #f5f5f5;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n  font-family: monospace;\r\n  overflow-x: auto;\r\n  display: block;\r\n  margin: 10px 0;\r\n}\r\n\r\n::v-deep .deepseek-response code {\r\n  background-color: #f5f5f5;\r\n  padding: 2px 4px;\r\n  border-radius: 3px;\r\n  font-family: monospace;\r\n}\r\n\r\n::v-deep .deepseek-response table {\r\n  border-collapse: collapse;\r\n  width: 100%;\r\n  margin: 15px 0;\r\n}\r\n\r\n::v-deep .deepseek-response th,\r\n::v-deep .deepseek-response td {\r\n  border: 1px solid #ddd;\r\n  padding: 8px;\r\n  text-align: left;\r\n}\r\n\r\n::v-deep .deepseek-response th {\r\n  background-color: #f2f2f2;\r\n  font-weight: bold;\r\n}\r\n\r\n::v-deep .deepseek-response h1,\r\n::v-deep .deepseek-response h2,\r\n::v-deep .deepseek-response h3,\r\n::v-deep .deepseek-response h4,\r\n::v-deep .deepseek-response h5,\r\n::v-deep .deepseek-response h6 {\r\n  margin-top: 20px;\r\n  margin-bottom: 10px;\r\n  font-weight: bold;\r\n  color: #222;\r\n}\r\n\r\n::v-deep .deepseek-response a {\r\n  color: #0066cc;\r\n  text-decoration: none;\r\n}\r\n\r\n::v-deep .deepseek-response blockquote {\r\n  border-left: 4px solid #ddd;\r\n  padding-left: 15px;\r\n  margin: 15px 0;\r\n  color: #555;\r\n}\r\n\r\n::v-deep .deepseek-response ul,\r\n::v-deep .deepseek-response ol {\r\n  padding-left: 20px;\r\n  margin: 10px 0;\r\n}\r\n\r\n/* 媒体选择区域样式 */\r\n.media-selection-section {\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 8px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.media-selection-section h3 {\r\n  margin: 0 0 12px 0;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.media-radio-group {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.media-radio-group .el-radio-button__inner {\r\n  padding: 8px 16px;\r\n  font-size: 13px;\r\n  border-radius: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.media-radio-group .el-radio-button__inner i {\r\n  font-size: 14px;\r\n}\r\n\r\n.media-description {\r\n  margin-top: 10px;\r\n  padding: 8px 12px;\r\n  background-color: #f0f9ff;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #409eff;\r\n}\r\n\r\n.media-description small {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.layout-prompt-section h3 {\r\n  margin-bottom: 10px;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n/* 微头条相关样式 */\r\n.tth-flow-dialog {\r\n  .tth-flow-content {\r\n    display: flex;\r\n    gap: 20px;\r\n    height: 600px;\r\n  }\r\n\r\n  .flow-logs-section,\r\n  .flow-images-section {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .flow-logs-section h3,\r\n  .flow-images-section h3 {\r\n    margin: 0 0 12px 0;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #303133;\r\n  }\r\n\r\n  .progress-timeline {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    border: 1px solid #e4e7ed;\r\n    border-radius: 4px;\r\n    padding: 12px;\r\n    background-color: #fafafa;\r\n  }\r\n\r\n  .timeline-scroll {\r\n    max-height: 500px;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .progress-item {\r\n    position: relative;\r\n    margin-bottom: 16px;\r\n    padding-left: 20px;\r\n  }\r\n\r\n  .progress-dot {\r\n    position: absolute;\r\n    left: 0;\r\n    top: 4px;\r\n    width: 8px;\r\n    height: 8px;\r\n    background-color: #67c23a;\r\n    border-radius: 50%;\r\n  }\r\n\r\n  .progress-line {\r\n    position: absolute;\r\n    left: 3px;\r\n    top: 12px;\r\n    width: 2px;\r\n    height: 20px;\r\n    background-color: #e4e7ed;\r\n  }\r\n\r\n  .progress-content {\r\n    .progress-time {\r\n      font-size: 12px;\r\n      color: #909399;\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .progress-text {\r\n      font-size: 13px;\r\n      color: #303133;\r\n      line-height: 1.4;\r\n    }\r\n  }\r\n\r\n  .flow-images-container {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    border: 1px solid #e4e7ed;\r\n    border-radius: 8px;\r\n    padding: 16px;\r\n    background-color: #fafafa;\r\n    max-height: 500px;\r\n  }\r\n\r\n  .flow-image-item {\r\n    margin-bottom: 20px;\r\n    text-align: center;\r\n  }\r\n\r\n  .flow-image-item:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .flow-image {\r\n    max-width: 100%;\r\n    max-height: 400px;\r\n    min-height: 200px;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    border: 2px solid #e4e7ed;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    transition: all 0.3s ease;\r\n    object-fit: contain;\r\n  }\r\n\r\n  .flow-image:hover {\r\n    transform: scale(1.02);\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n    border-color: #409eff;\r\n  }\r\n\r\n  .no-logs {\r\n    text-align: center;\r\n    color: #909399;\r\n    font-size: 13px;\r\n    padding: 20px;\r\n  }\r\n}\r\n\r\n.tth-article-edit-dialog {\r\n  .tth-article-edit-content {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 20px;\r\n  }\r\n\r\n  .article-title-section h3,\r\n  .article-content-section h3 {\r\n    margin: 0 0 8px 0;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #303133;\r\n  }\r\n\r\n  .article-title-input {\r\n    width: 100%;\r\n  }\r\n\r\n  .article-content-input {\r\n    width: 100%;\r\n  }\r\n\r\n  .content-input-wrapper {\r\n    position: relative;\r\n  }\r\n\r\n  .content-length-info {\r\n    position: absolute;\r\n    bottom: 8px;\r\n    right: 8px;\r\n    font-size: 12px;\r\n    color: #909399;\r\n    background-color: rgba(255, 255, 255, 0.9);\r\n    padding: 2px 6px;\r\n    border-radius: 3px;\r\n    z-index: 1;\r\n  }\r\n\r\n  .text-danger {\r\n    color: #f56c6c !important;\r\n    font-weight: 600;\r\n  }\r\n\r\n  .content-over-limit .el-textarea__inner {\r\n    border-color: #f56c6c !important;\r\n    box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2) !important;\r\n  }\r\n}\r\n</style>\r\n"]}]}